import 'package:supplier_app/core/services/firebase_promotions_service.dart';
import 'package:supplier_app/data/models/promotion.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';

class PromotionRepository {
  final FirebasePromotionsService _firebasePromotionsService;
  final AuthRepository _authRepository;

  PromotionRepository({
    FirebasePromotionsService? firebasePromotionsService,
    AuthRepository? authRepository,
  })  : _firebasePromotionsService =
            firebasePromotionsService ?? FirebasePromotionsService(),
        _authRepository = authRepository ?? AuthRepository();

  // Get current restaurant ID from auth
  Future<String> _getRestaurantId() async {
    final user = await _authRepository.getCurrentUser();
    if (user == null) throw PromotionException('Not authenticated');
    return user.id; // Assuming user.id is the restaurant ID
  }

  // Get all promotions
  Future<List<Promotion>> getPromotions({
    bool? isActive,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebasePromotionsService.getPromotions(
        restaurantId,
        isActive: isActive,
        limit: limit,
      );
    } catch (e) {
      throw PromotionException('Failed to fetch promotions: ${e.toString()}');
    }
  }

  // Get promotions stream for real-time updates
  Stream<List<Promotion>> getPromotionsStream({bool? isActive}) async* {
    try {
      final restaurantId = await _getRestaurantId();
      yield* _firebasePromotionsService.getPromotionsStream(restaurantId,
          isActive: isActive);
    } catch (e) {
      throw PromotionException(
          'Failed to get promotions stream: ${e.toString()}');
    }
  }

  // Get promotion by ID
  Future<Promotion?> getPromotionById(String promotionId) async {
    try {
      return await _firebasePromotionsService.getPromotion(promotionId);
    } catch (e) {
      throw PromotionException('Failed to fetch promotion: ${e.toString()}');
    }
  }

  // Create promotion
  Future<Promotion> createPromotion(Promotion promotion) async {
    try {
      final restaurantId = await _getRestaurantId();
      final promotionWithRestaurant = promotion.copyWith(
        restaurantId: restaurantId,
      );

      return await _firebasePromotionsService
          .createPromotion(promotionWithRestaurant);
    } catch (e) {
      throw PromotionException('Failed to create promotion: ${e.toString()}');
    }
  }

  // Update promotion
  Future<Promotion> updatePromotion(Promotion promotion) async {
    try {
      return await _firebasePromotionsService.updatePromotion(promotion);
    } catch (e) {
      throw PromotionException('Failed to update promotion: ${e.toString()}');
    }
  }

  // Delete promotion
  Future<void> deletePromotion(String promotionId) async {
    try {
      await _firebasePromotionsService.deletePromotion(promotionId);
    } catch (e) {
      throw PromotionException('Failed to delete promotion: ${e.toString()}');
    }
  }

  // Toggle promotion status
  Future<Promotion> togglePromotionStatus(
      String promotionId, bool isActive) async {
    try {
      await _firebasePromotionsService.togglePromotionStatus(
          promotionId, isActive);
      // Fetch and return the updated promotion
      final updatedPromotion = await getPromotionById(promotionId);
      if (updatedPromotion == null) {
        throw PromotionException('Promotion not found after status toggle');
      }
      return updatedPromotion;
    } catch (e) {
      throw PromotionException(
          'Failed to toggle promotion status: ${e.toString()}');
    }
  }

  // Get active promotions for customers
  Future<List<Promotion>> getActivePromotions() async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebasePromotionsService.getActivePromotions(restaurantId);
    } catch (e) {
      throw PromotionException(
          'Failed to fetch active promotions: ${e.toString()}');
    }
  }

  // Get promotion by promo code
  Future<Promotion?> getPromotionByCode(String promoCode) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebasePromotionsService.getPromotionByCode(
          promoCode, restaurantId);
    } catch (e) {
      throw PromotionException(
          'Failed to fetch promotion by code: ${e.toString()}');
    }
  }

  // Record promotion usage
  Future<void> recordPromotionUsage(
    String promotionId,
    String orderId,
    String customerId,
    double discountAmount,
  ) async {
    try {
      await _firebasePromotionsService.recordPromotionUsage(
        promotionId,
        orderId,
        customerId,
        discountAmount,
      );
    } catch (e) {
      throw PromotionException(
          'Failed to record promotion usage: ${e.toString()}');
    }
  }

  // Get promotion analytics
  Future<Map<String, dynamic>> getPromotionAnalytics(
    String promotionId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _firebasePromotionsService.getPromotionAnalytics(
        promotionId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw PromotionException(
          'Failed to fetch promotion analytics: ${e.toString()}');
    }
  }

  // Get promotions summary
  Future<Map<String, dynamic>> getPromotionsSummary() async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebasePromotionsService
          .getPromotionsSummary(restaurantId);
    } catch (e) {
      throw PromotionException(
          'Failed to fetch promotions summary: ${e.toString()}');
    }
  }

  // Get promotion usage data
  Future<List<PromotionUsage>> getPromotionUsage(
    String promotionId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // For now, return mock data since we don't have a specific usage collection
      // In a real implementation, you would query a promotion_usage collection
      final now = DateTime.now();
      return [
        PromotionUsage(
          id: 'usage1',
          promotionId: promotionId,
          orderId: 'order1',
          customerId: 'customer1',
          discountAmount: 25.99,
          usedAt: now.subtract(const Duration(days: 7)),
        ),
        PromotionUsage(
          id: 'usage2',
          promotionId: promotionId,
          orderId: 'order2',
          customerId: 'customer2',
          discountAmount: 40.50,
          usedAt: now.subtract(const Duration(days: 6)),
        ),
        PromotionUsage(
          id: 'usage3',
          promotionId: promotionId,
          orderId: 'order3',
          customerId: 'customer3',
          discountAmount: 15.75,
          usedAt: now.subtract(const Duration(days: 5)),
        ),
      ];
    } catch (e) {
      throw PromotionException(
          'Failed to fetch promotion usage: ${e.toString()}');
    }
  }
}

class PromotionException implements Exception {
  final String message;

  const PromotionException(this.message);

  @override
  String toString() => 'PromotionException: $message';
}
