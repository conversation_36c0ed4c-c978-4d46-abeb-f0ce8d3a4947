class AppConstants {
  // API Constants
  static const String baseUrl = 'https://api.foodieapp.com';
  static const String apiVersion = '/v1';
  static const String apiKey = 'your_api_key_here';

  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String restaurantDataKey = 'restaurant_data';
  static const String settingsKey = 'app_settings';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Image Constants
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];

  // Order Constants
  static const int orderRefreshInterval = 30; // seconds
  static const int maxOrderItems = 50;

  // Menu Constants
  static const int maxMenuCategories = 20;
  static const int maxDishesPerCategory = 100;
  static const double minPrice = 0.01;
  static const double maxPrice = 9999.99;

  // Validation Constants
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int maxRestaurantNameLength = 100;
  static const int maxDescriptionLength = 500;
  static const int maxAddressLength = 200;

  // Time Constants
  static const int sessionTimeoutMinutes = 30;
  static const int cacheExpiryHours = 24;

  // Notification Constants
  static const String newOrderChannel = 'new_orders';
  static const String generalChannel = 'general';

  // Error Messages
  static const String networkError =
      'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String authError = 'Authentication failed. Please login again.';
  static const String validationError =
      'Please check your input and try again.';
  static const String unknownError =
      'An unexpected error occurred. Please try again.';
}

// Enums
enum OrderStatus {
  pending,
  accepted,
  preparing,
  ready,
  pickedUp,
  delivered,
  cancelled,
  rejected
}

enum PaymentStatus { pending, paid, failed, refunded }

enum RestaurantStatus { online, offline, busy }

enum DishType { veg, nonVeg, vegan, glutenFree }

enum PromotionType { percentage, fixedAmount, buyOneGetOne, freeDelivery }

enum ReportType { daily, weekly, monthly, yearly, custom }

enum NotificationType { newOrder, orderUpdate, payment, promotion, system }

// Extensions for better enum handling
extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.accepted:
        return 'Accepted';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready for Pickup';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.rejected:
        return 'Rejected';
    }
  }

  bool get canAccept => this == OrderStatus.pending;
  bool get canReject => this == OrderStatus.pending;
  bool get canPrepare => this == OrderStatus.accepted;
  bool get canMarkReady => this == OrderStatus.preparing;
  bool get canMarkPickedUp => this == OrderStatus.ready;
  bool get canCancel => [
        OrderStatus.pending,
        OrderStatus.accepted,
        OrderStatus.preparing
      ].contains(this);
}

extension RestaurantStatusExtension on RestaurantStatus {
  String get displayName {
    switch (this) {
      case RestaurantStatus.online:
        return 'Online';
      case RestaurantStatus.offline:
        return 'Offline';
      case RestaurantStatus.busy:
        return 'Busy';
    }
  }
}

extension DishTypeExtension on DishType {
  String get displayName {
    switch (this) {
      case DishType.veg:
        return 'Vegetarian';
      case DishType.nonVeg:
        return 'Non-Vegetarian';
      case DishType.vegan:
        return 'Vegan';
      case DishType.glutenFree:
        return 'Gluten Free';
    }
  }

  String get shortName {
    switch (this) {
      case DishType.veg:
        return 'Veg';
      case DishType.nonVeg:
        return 'Non-Veg';
      case DishType.vegan:
        return 'Vegan';
      case DishType.glutenFree:
        return 'GF';
    }
  }
}

extension PromotionTypeExtension on PromotionType {
  String get displayName {
    switch (this) {
      case PromotionType.percentage:
        return 'Percentage Discount';
      case PromotionType.fixedAmount:
        return 'Fixed Amount Discount';
      case PromotionType.buyOneGetOne:
        return 'Buy One Get One';
      case PromotionType.freeDelivery:
        return 'Free Delivery';
    }
  }
}
