import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/presentation/bloc/orders/orders_bloc.dart';
import 'package:supplier_app/presentation/bloc/orders/orders_event.dart';
import 'package:supplier_app/presentation/bloc/orders/orders_state.dart';
import 'package:supplier_app/presentation/widgets/orders/order_card.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  OrderStatus? _currentFilter;

  final List<Tab> _tabs = [
    const Tab(text: 'All'),
    const Tab(text: 'New'),
    const Tab(text: 'Preparing'),
    const Tab(text: 'Ready'),
    const Tab(text: 'Completed'),
    const Tab(text: 'Cancelled'),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_onTabChanged);
    
    // Load initial orders
    context.read<OrdersBloc>().add(const OrdersLoadRequested());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;
    
    OrderStatus? newFilter;
    switch (_tabController.index) {
      case 0: // All
        newFilter = null;
        break;
      case 1: // New
        newFilter = OrderStatus.pending;
        break;
      case 2: // Preparing
        newFilter = OrderStatus.preparing;
        break;
      case 3: // Ready
        newFilter = OrderStatus.ready;
        break;
      case 4: // Completed
        newFilter = OrderStatus.delivered;
        break;
      case 5: // Cancelled
        newFilter = OrderStatus.cancelled;
        break;
    }
    
    if (newFilter != _currentFilter) {
      _currentFilter = newFilter;
      context.read<OrdersBloc>().add(OrdersFilterChanged(status: newFilter));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: _tabs,
            ),
          ),
          
          // Orders List
          Expanded(
            child: BlocConsumer<OrdersBloc, OrdersState>(
              listener: (context, state) {
                if (state is OrdersError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                } else if (state is OrderUpdateSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Order updated successfully'),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  );
                } else if (state is OrderUpdateError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is OrdersLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is OrdersError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load orders',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            context.read<OrdersBloc>().add(
                              OrdersLoadRequested(status: _currentFilter),
                            );
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (state is OrdersLoaded) {
                  final filteredOrders = _getFilteredOrders(state);
                  
                  if (filteredOrders.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No orders found',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getEmptyMessage(),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<OrdersBloc>().add(
                        OrdersLoadRequested(
                          status: _currentFilter,
                          isRefresh: true,
                        ),
                      );
                    },
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredOrders.length,
                      itemBuilder: (context, index) {
                        final order = filteredOrders[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: OrderCard(
                            order: order,
                            onTap: () {
                              // TODO: Navigate to order details
                            },
                            onAccept: order.status.canAccept
                                ? () {
                                    context.read<OrdersBloc>().add(
                                      OrderAcceptRequested(order.id),
                                    );
                                  }
                                : null,
                            onReject: order.status.canReject
                                ? () {
                                    _showRejectDialog(context, order.id);
                                  }
                                : null,
                            onStatusUpdate: (status) {
                              context.read<OrdersBloc>().add(
                                OrderStatusUpdateRequested(order.id, status),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  List<dynamic> _getFilteredOrders(OrdersLoaded state) {
    switch (_tabController.index) {
      case 0: // All
        return state.orders;
      case 1: // New
        return state.pendingOrders;
      case 2: // Preparing
        return state.preparingOrders;
      case 3: // Ready
        return state.readyOrders;
      case 4: // Completed
        return state.completedOrders;
      case 5: // Cancelled
        return state.cancelledOrders;
      default:
        return state.orders;
    }
  }

  String _getEmptyMessage() {
    switch (_tabController.index) {
      case 1:
        return 'No new orders at the moment';
      case 2:
        return 'No orders being prepared';
      case 3:
        return 'No orders ready for pickup';
      case 4:
        return 'No completed orders';
      case 5:
        return 'No cancelled orders';
      default:
        return 'No orders found';
    }
  }

  void _showRejectDialog(BuildContext context, String orderId) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Reject Order'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejecting this order:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                hintText: 'Enter rejection reason...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.trim().isNotEmpty) {
                Navigator.of(dialogContext).pop();
                context.read<OrdersBloc>().add(
                  OrderRejectRequested(orderId, reasonController.text.trim()),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }
}
