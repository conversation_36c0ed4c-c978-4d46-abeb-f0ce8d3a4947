import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // Stream controller for connectivity status
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  
  bool _isConnected = true;
  bool _isInitialized = false;

  // Public stream for listening to connectivity changes
  Stream<bool> get connectivityStream => _connectivityController.stream;
  
  // Current connectivity status
  bool get isConnected => _isConnected;

  // Initialize connectivity monitoring
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      final connectivityResults = await _connectivity.checkConnectivity();
      _updateConnectionStatus(connectivityResults);

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateConnectionStatus,
        onError: (error) {
          debugPrint('Connectivity error: $error');
          _updateConnectionStatus([ConnectivityResult.none]);
        },
      );

      _isInitialized = true;
      debugPrint('ConnectivityService initialized. Initial status: $_isConnected');
    } catch (e) {
      debugPrint('Failed to initialize ConnectivityService: $e');
      // Assume connected if we can't check
      _isConnected = true;
      _connectivityController.add(_isConnected);
    }
  }

  // Update connection status based on connectivity results
  void _updateConnectionStatus(List<ConnectivityResult> connectivityResults) {
    final wasConnected = _isConnected;
    
    // Check if any connection type is available
    _isConnected = connectivityResults.any((result) => 
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet ||
      result == ConnectivityResult.vpn
    );

    // Only emit if status changed
    if (wasConnected != _isConnected) {
      debugPrint('Connectivity changed: $_isConnected');
      _connectivityController.add(_isConnected);
    }
  }

  // Check current connectivity status
  Future<bool> checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      _updateConnectionStatus(connectivityResults);
      return _isConnected;
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      return _isConnected; // Return last known status
    }
  }

  // Wait for connection to be available
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (_isConnected) return true;

    try {
      // Wait for connectivity change or timeout
      final result = await connectivityStream
          .where((isConnected) => isConnected)
          .timeout(timeout)
          .first;
      
      return result;
    } on TimeoutException {
      debugPrint('Timeout waiting for connection');
      return false;
    } catch (e) {
      debugPrint('Error waiting for connection: $e');
      return false;
    }
  }

  // Execute a function with connectivity check
  Future<T?> executeWithConnectivity<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
    bool waitForConnection = true,
  }) async {
    // Check if we're connected
    if (!_isConnected) {
      if (waitForConnection) {
        debugPrint('No connection, waiting for connectivity...');
        final connected = await this.waitForConnection(timeout: timeout);
        if (!connected) {
          throw ConnectivityException('No internet connection available');
        }
      } else {
        throw ConnectivityException('No internet connection');
      }
    }

    try {
      return await operation();
    } catch (e) {
      // Check if error might be due to connectivity
      if (e.toString().toLowerCase().contains('network') ||
          e.toString().toLowerCase().contains('connection') ||
          e.toString().toLowerCase().contains('timeout')) {
        
        // Recheck connectivity
        await checkConnectivity();
        if (!_isConnected) {
          throw ConnectivityException('Lost internet connection during operation');
        }
      }
      rethrow;
    }
  }

  // Get connection type details
  Future<String> getConnectionType() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      
      if (connectivityResults.contains(ConnectivityResult.wifi)) {
        return 'WiFi';
      } else if (connectivityResults.contains(ConnectivityResult.mobile)) {
        return 'Mobile Data';
      } else if (connectivityResults.contains(ConnectivityResult.ethernet)) {
        return 'Ethernet';
      } else if (connectivityResults.contains(ConnectivityResult.vpn)) {
        return 'VPN';
      } else {
        return 'No Connection';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  // Check if connection is metered (mobile data)
  Future<bool> isMeteredConnection() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      return connectivityResults.contains(ConnectivityResult.mobile);
    } catch (e) {
      return false;
    }
  }

  // Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
    _isInitialized = false;
  }
}

class ConnectivityException implements Exception {
  final String message;
  
  const ConnectivityException(this.message);
  
  @override
  String toString() => 'ConnectivityException: $message';
}
