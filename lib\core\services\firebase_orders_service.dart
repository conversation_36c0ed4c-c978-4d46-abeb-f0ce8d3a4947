import 'package:cloud_firestore/cloud_firestore.dart' hide Order;
import 'package:flutter/foundation.dart';
import 'package:supplier_app/data/models/order.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

class FirebaseOrdersService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String _ordersCollection = 'orders';
  static const String _restaurantsCollection = 'restaurants';

  // Real-time orders stream for a restaurant
  Stream<List<Order>> getOrdersStream(String restaurantId,
      {OrderStatus? status}) {
    Query query = _firestore
        .collection(_ordersCollection)
        .where('restaurantId', isEqualTo: restaurantId)
        .orderBy('orderTime', descending: true);

    if (status != null) {
      query = query.where('status', isEqualTo: status.name);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Order.fromJson(data);
      }).toList();
    });
  }

  // Get orders with pagination
  Future<List<Order>> getOrders(
    String restaurantId, {
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .orderBy('orderTime', descending: true)
          .limit(limit);

      if (status != null) {
        query = query.where('status', isEqualTo: status.name);
      }

      if (startDate != null) {
        query = query.where('orderTime',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('orderTime',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Order.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching orders: $e');
      throw Exception('Failed to fetch orders: $e');
    }
  }

  // Get single order
  Future<Order?> getOrder(String orderId) async {
    try {
      final doc =
          await _firestore.collection(_ordersCollection).doc(orderId).get();
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      return Order.fromJson(data);
    } catch (e) {
      debugPrint('Error fetching order: $e');
      throw Exception('Failed to fetch order: $e');
    }
  }

  // Update order status
  Future<void> updateOrderStatus(
    String orderId,
    OrderStatus status, {
    String? rejectionReason,
    int? estimatedPreparationTime,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.name,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Add timestamp for specific status changes
      switch (status) {
        case OrderStatus.accepted:
          updateData['acceptedAt'] = FieldValue.serverTimestamp();
          if (estimatedPreparationTime != null) {
            updateData['estimatedPreparationTime'] = estimatedPreparationTime;
          }
          break;
        case OrderStatus.preparing:
          updateData['preparingAt'] = FieldValue.serverTimestamp();
          break;
        case OrderStatus.ready:
          updateData['readyAt'] = FieldValue.serverTimestamp();
          break;
        case OrderStatus.pickedUp:
          updateData['pickedUpAt'] = FieldValue.serverTimestamp();
          break;
        case OrderStatus.delivered:
          updateData['deliveredAt'] = FieldValue.serverTimestamp();
          break;
        case OrderStatus.cancelled:
          updateData['cancellationReason'] =
              rejectionReason ?? 'Order cancelled';
          break;
        case OrderStatus.rejected:
          updateData['rejectionReason'] = rejectionReason ?? 'Order rejected';
          break;
        default:
          break;
      }

      await _firestore
          .collection(_ordersCollection)
          .doc(orderId)
          .update(updateData);
    } catch (e) {
      debugPrint('Error updating order status: $e');
      throw Exception('Failed to update order status: $e');
    }
  }

  // Accept order
  Future<void> acceptOrder(String orderId,
      {int? estimatedPreparationTime}) async {
    await updateOrderStatus(
      orderId,
      OrderStatus.accepted,
      estimatedPreparationTime: estimatedPreparationTime,
    );
  }

  // Reject order
  Future<void> rejectOrder(String orderId, String reason) async {
    await updateOrderStatus(
      orderId,
      OrderStatus.rejected,
      rejectionReason: reason,
    );
  }

  // Get today's orders summary
  Future<Map<String, dynamic>> getTodaysSummary(String restaurantId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

      final snapshot = await _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('orderTime',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('orderTime', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .get();

      int newOrders = 0;
      int preparingOrders = 0;
      int completedOrders = 0;
      int cancelledOrders = 0;
      double totalSales = 0.0;

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final status = OrderStatus.values.firstWhere(
          (e) => e.name == data['status'],
          orElse: () => OrderStatus.pending,
        );

        switch (status) {
          case OrderStatus.pending:
            newOrders++;
            break;
          case OrderStatus.preparing:
          case OrderStatus.accepted:
            preparingOrders++;
            break;
          case OrderStatus.delivered:
            completedOrders++;
            totalSales += (data['total'] as num?)?.toDouble() ?? 0.0;
            break;
          case OrderStatus.cancelled:
          case OrderStatus.rejected:
            cancelledOrders++;
            break;
          default:
            break;
        }
      }

      return {
        'newOrders': newOrders,
        'preparingOrders': preparingOrders,
        'completedOrders': completedOrders,
        'cancelledOrders': cancelledOrders,
        'totalSales': totalSales,
        'totalOrders': snapshot.docs.length,
      };
    } catch (e) {
      debugPrint('Error fetching today\'s summary: $e');
      throw Exception('Failed to fetch today\'s summary: $e');
    }
  }

  // Get orders by status counts
  Future<Map<OrderStatus, int>> getOrderStatusCounts(
      String restaurantId) async {
    try {
      final snapshot = await _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .get();

      final counts = <OrderStatus, int>{};
      for (final status in OrderStatus.values) {
        counts[status] = 0;
      }

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final status = OrderStatus.values.firstWhere(
          (e) => e.name == data['status'],
          orElse: () => OrderStatus.pending,
        );
        counts[status] = (counts[status] ?? 0) + 1;
      }

      return counts;
    } catch (e) {
      debugPrint('Error fetching order status counts: $e');
      throw Exception('Failed to fetch order status counts: $e');
    }
  }

  // Create order (for testing purposes)
  Future<String> createOrder(Order order) async {
    try {
      final docRef =
          await _firestore.collection(_ordersCollection).add(order.toJson());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating order: $e');
      throw Exception('Failed to create order: $e');
    }
  }
}
