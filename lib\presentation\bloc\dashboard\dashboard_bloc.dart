import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/data/repositories/order_repository.dart';
import 'package:supplier_app/data/repositories/analytics_repository.dart';
import 'dashboard_event.dart';
import 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final OrderRepository orderRepository;
  final AnalyticsRepository analyticsRepository;
  RestaurantStatus _currentStatus = RestaurantStatus.offline;

  DashboardBloc({
    required this.orderRepository,
    required this.analyticsRepository,
  }) : super(const DashboardInitial()) {
    on<DashboardDataRequested>(_onDashboardDataRequested);
    on<DashboardRefreshRequested>(_onDashboardRefreshRequested);
    on<RestaurantStatusToggleRequested>(_onRestaurantStatusToggleRequested);
    on<DashboardOrderCountsRequested>(_onDashboardOrderCountsRequested);
    on<DashboardTodaysSummaryRequested>(_onDashboardTodaysSummaryRequested);
  }

  Future<void> _onDashboardDataRequested(
    DashboardDataRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    try {
      // Fetch order counts and today's summary in parallel
      final results = await Future.wait([
        orderRepository.getOrderCounts(),
        orderRepository.getTodaysSummary(),
      ]);

      final orderCounts = results[0] as Map<OrderStatus, int>;
      final todaysSummary = results[1] as Map<String, dynamic>;

      emit(DashboardLoaded(
        orderCounts: orderCounts,
        todaysSummary: todaysSummary,
        restaurantStatus: _currentStatus,
      ));
    } catch (e) {
      emit(DashboardError(e.toString()));
    }
  }

  Future<void> _onDashboardRefreshRequested(
    DashboardRefreshRequested event,
    Emitter<DashboardState> emit,
  ) async {
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(currentState.copyWith(isRefreshing: true));

      try {
        // Fetch updated data
        final results = await Future.wait([
          orderRepository.getOrderCounts(),
          orderRepository.getTodaysSummary(),
        ]);

        final orderCounts = results[0] as Map<OrderStatus, int>;
        final todaysSummary = results[1] as Map<String, dynamic>;

        emit(DashboardLoaded(
          orderCounts: orderCounts,
          todaysSummary: todaysSummary,
          restaurantStatus: _currentStatus,
          isRefreshing: false,
        ));
      } catch (e) {
        emit(currentState.copyWith(isRefreshing: false));
        emit(DashboardError(e.toString()));
      }
    } else {
      // If not loaded, perform initial load
      add(const DashboardDataRequested());
    }
  }

  Future<void> _onRestaurantStatusToggleRequested(
    RestaurantStatusToggleRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final previousStatus = _currentStatus;
    emit(RestaurantStatusUpdating(event.status));

    try {
      // TODO: Implement API call to update restaurant status
      // For now, we'll simulate the update
      await Future.delayed(const Duration(seconds: 1));

      _currentStatus = event.status;
      emit(RestaurantStatusUpdateSuccess(event.status));

      // Update the dashboard with new status
      if (state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        emit(currentState.copyWith(restaurantStatus: event.status));
      }
    } catch (e) {
      emit(RestaurantStatusUpdateError(e.toString(), previousStatus));
    }
  }

  Future<void> _onDashboardOrderCountsRequested(
    DashboardOrderCountsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final orderCounts = await orderRepository.getOrderCounts();

      if (state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        emit(currentState.copyWith(orderCounts: orderCounts));
      }
    } catch (e) {
      emit(DashboardError(e.toString()));
    }
  }

  Future<void> _onDashboardTodaysSummaryRequested(
    DashboardTodaysSummaryRequested event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final todaysSummary = await orderRepository.getTodaysSummary();

      if (state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        emit(currentState.copyWith(todaysSummary: todaysSummary));
      }
    } catch (e) {
      emit(DashboardError(e.toString()));
    }
  }
}
