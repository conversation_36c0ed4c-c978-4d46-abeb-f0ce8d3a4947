{"project_info": {"project_number": "672277532313", "project_id": "foodie-33530", "storage_bucket": "foodie-33530.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:672277532313:android:bde38c762c57e837f5b208", "android_client_info": {"package_name": "com.example.foodie_app"}}, "oauth_client": [{"client_id": "672277532313-2aprqe3mtak2riqest2lc595e4mvftgh.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.foodie_app", "certificate_hash": "9970b1a54d5c0aea4cc26ace64ba832cb599bc68"}}, {"client_id": "672277532313-h05a4s4bdj0ucq0b1i28q7sda7viinam.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDkUtoSgTvVSzR8v_PhYamqtjwPc2tLj4A"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "672277532313-h05a4s4bdj0ucq0b1i28q7sda7viinam.apps.googleusercontent.com", "client_type": 3}, {"client_id": "672277532313-h26e890htsngfhto8kk4ae2u11f9epjl.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.foodieApp"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:672277532313:android:bf514acfcfdc2d06f5b208", "android_client_info": {"package_name": "com.example.supplier_app"}}, "oauth_client": [{"client_id": "672277532313-h05a4s4bdj0ucq0b1i28q7sda7viinam.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDkUtoSgTvVSzR8v_PhYamqtjwPc2tLj4A"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "672277532313-h05a4s4bdj0ucq0b1i28q7sda7viinam.apps.googleusercontent.com", "client_type": 3}, {"client_id": "672277532313-h26e890htsngfhto8kk4ae2u11f9epjl.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.foodieApp"}}]}}}], "configuration_version": "1"}