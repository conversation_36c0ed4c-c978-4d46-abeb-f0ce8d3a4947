import 'package:supplier_app/core/services/firebase_orders_service.dart';
import 'package:supplier_app/data/models/order.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

class OrderRepository {
  final FirebaseOrdersService _firebaseOrdersService;
  final AuthRepository _authRepository;

  OrderRepository({
    FirebaseOrdersService? firebaseOrdersService,
    AuthRepository? authRepository,
  })  : _firebaseOrdersService =
            firebaseOrdersService ?? FirebaseOrdersService(),
        _authRepository = authRepository ?? AuthRepository();

  // Get current restaurant ID from auth
  Future<String> _getRestaurantId() async {
    final user = await _authRepository.getCurrentUser();
    if (user == null) throw OrderException('Not authenticated');
    return user.id; // Assuming user.id is the restaurant ID
  }

  // Get orders with filters
  Future<List<Order>> getOrders({
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseOrdersService.getOrders(
        restaurantId,
        status: status,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      throw OrderException('Failed to fetch orders: ${e.toString()}');
    }
  }

  // Get orders stream for real-time updates
  Stream<List<Order>> getOrdersStream({OrderStatus? status}) async* {
    try {
      final restaurantId = await _getRestaurantId();
      yield* _firebaseOrdersService.getOrdersStream(restaurantId,
          status: status);
    } catch (e) {
      throw OrderException('Failed to get orders stream: ${e.toString()}');
    }
  }

  // Get order by ID
  Future<Order?> getOrderById(String orderId) async {
    try {
      return await _firebaseOrdersService.getOrder(orderId);
    } catch (e) {
      throw OrderException('Failed to fetch order: ${e.toString()}');
    }
  }

  // Accept order
  Future<Order> acceptOrder(String orderId,
      {int? estimatedPreparationTime}) async {
    try {
      await _firebaseOrdersService.acceptOrder(
        orderId,
        estimatedPreparationTime: estimatedPreparationTime,
      );
      // Fetch and return the updated order
      final updatedOrder = await getOrderById(orderId);
      if (updatedOrder == null) {
        throw OrderException('Order not found after acceptance');
      }
      return updatedOrder;
    } catch (e) {
      throw OrderException('Failed to accept order: ${e.toString()}');
    }
  }

  // Reject order
  Future<Order> rejectOrder(String orderId, String reason) async {
    try {
      await _firebaseOrdersService.rejectOrder(orderId, reason);
      // Fetch and return the updated order
      final updatedOrder = await getOrderById(orderId);
      if (updatedOrder == null) {
        throw OrderException('Order not found after rejection');
      }
      return updatedOrder;
    } catch (e) {
      throw OrderException('Failed to reject order: ${e.toString()}');
    }
  }

  // Update order status
  Future<Order> updateOrderStatus(String orderId, OrderStatus status) async {
    try {
      await _firebaseOrdersService.updateOrderStatus(orderId, status);
      // Fetch and return the updated order
      final updatedOrder = await getOrderById(orderId);
      if (updatedOrder == null) {
        throw OrderException('Order not found after status update');
      }
      return updatedOrder;
    } catch (e) {
      throw OrderException('Failed to update order status: ${e.toString()}');
    }
  }

  // Get order counts by status
  Future<Map<OrderStatus, int>> getOrderCounts() async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseOrdersService.getOrderStatusCounts(restaurantId);
    } catch (e) {
      throw OrderException('Failed to fetch order counts: ${e.toString()}');
    }
  }

  // Get today's orders summary
  Future<Map<String, dynamic>> getTodaysSummary() async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseOrdersService.getTodaysSummary(restaurantId);
    } catch (e) {
      throw OrderException('Failed to fetch today\'s summary: ${e.toString()}');
    }
  }

  // Create order (for testing purposes)
  Future<String> createOrder(Order order) async {
    try {
      return await _firebaseOrdersService.createOrder(order);
    } catch (e) {
      throw OrderException('Failed to create order: ${e.toString()}');
    }
  }
}

class OrderException implements Exception {
  final String message;

  const OrderException(this.message);

  @override
  String toString() => 'OrderException: $message';
}
