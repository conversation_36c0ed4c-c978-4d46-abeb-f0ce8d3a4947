import 'package:flutter/material.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

class RestaurantStatusToggle extends StatelessWidget {
  final RestaurantStatus currentStatus;
  final Function(RestaurantStatus) onStatusChanged;

  const RestaurantStatusToggle({
    super.key,
    required this.currentStatus,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatusButton(
            context,
            RestaurantStatus.online,
            'Online',
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatusButton(
            context,
            RestaurantStatus.busy,
            'Busy',
            Icons.schedule,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatusButton(
            context,
            RestaurantStatus.offline,
            'Offline',
            Icons.cancel,
            Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusButton(
    BuildContext context,
    RestaurantStatus status,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = currentStatus == status;
    
    return GestureDetector(
      onTap: () => onStatusChanged(status),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          border: Border.all(
            color: color,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : color,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
