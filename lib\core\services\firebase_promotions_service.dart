import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:supplier_app/data/models/promotion.dart';

class FirebasePromotionsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String _promotionsCollection = 'promotions';
  static const String _promotionUsageCollection = 'promotion_usage';

  // Get promotions stream
  Stream<List<Promotion>> getPromotionsStream(String restaurantId, {bool? isActive}) {
    Query query = _firestore
        .collection(_promotionsCollection)
        .where('restaurantId', isEqualTo: restaurantId)
        .orderBy('createdAt', descending: true);

    if (isActive != null) {
      query = query.where('isActive', isEqualTo: isActive);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Promotion.fromJson(data);
      }).toList();
    });
  }

  // Get promotions
  Future<List<Promotion>> getPromotions(
    String restaurantId, {
    bool? isActive,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(_promotionsCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (isActive != null) {
        query = query.where('isActive', isEqualTo: isActive);
      }

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Promotion.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching promotions: $e');
      throw Exception('Failed to fetch promotions: $e');
    }
  }

  // Get single promotion
  Future<Promotion?> getPromotion(String promotionId) async {
    try {
      final doc = await _firestore.collection(_promotionsCollection).doc(promotionId).get();
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      return Promotion.fromJson(data);
    } catch (e) {
      debugPrint('Error fetching promotion: $e');
      throw Exception('Failed to fetch promotion: $e');
    }
  }

  // Create promotion
  Future<Promotion> createPromotion(Promotion promotion) async {
    try {
      final promotionData = promotion.toJson();
      promotionData.remove('id'); // Remove ID for creation
      promotionData['createdAt'] = FieldValue.serverTimestamp();
      promotionData['updatedAt'] = FieldValue.serverTimestamp();
      promotionData['usageCount'] = 0; // Initialize usage count

      // Generate promo code if not provided
      if (promotionData['promoCode'] == null || promotionData['promoCode'].isEmpty) {
        promotionData['promoCode'] = _generatePromoCode();
      }

      final docRef = await _firestore.collection(_promotionsCollection).add(promotionData);
      
      // Return promotion with generated ID
      return promotion.copyWith(
        id: docRef.id,
        promoCode: promotionData['promoCode'],
      );
    } catch (e) {
      debugPrint('Error creating promotion: $e');
      throw Exception('Failed to create promotion: $e');
    }
  }

  // Update promotion
  Future<Promotion> updatePromotion(Promotion promotion) async {
    try {
      final promotionData = promotion.toJson();
      promotionData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(_promotionsCollection).doc(promotion.id).update(promotionData);
      return promotion;
    } catch (e) {
      debugPrint('Error updating promotion: $e');
      throw Exception('Failed to update promotion: $e');
    }
  }

  // Toggle promotion status
  Future<void> togglePromotionStatus(String promotionId, bool isActive) async {
    try {
      await _firestore.collection(_promotionsCollection).doc(promotionId).update({
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error toggling promotion status: $e');
      throw Exception('Failed to toggle promotion status: $e');
    }
  }

  // Delete promotion
  Future<void> deletePromotion(String promotionId) async {
    try {
      await _firestore.collection(_promotionsCollection).doc(promotionId).delete();
    } catch (e) {
      debugPrint('Error deleting promotion: $e');
      throw Exception('Failed to delete promotion: $e');
    }
  }

  // Get active promotions for customers
  Future<List<Promotion>> getActivePromotions(String restaurantId) async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection(_promotionsCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('isActive', isEqualTo: true)
          .where('startDate', isLessThanOrEqualTo: Timestamp.fromDate(now))
          .where('endDate', isGreaterThanOrEqualTo: Timestamp.fromDate(now))
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return Promotion.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching active promotions: $e');
      throw Exception('Failed to fetch active promotions: $e');
    }
  }

  // Get promotion by promo code
  Future<Promotion?> getPromotionByCode(String promoCode, String restaurantId) async {
    try {
      final snapshot = await _firestore
          .collection(_promotionsCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('promoCode', isEqualTo: promoCode)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) return null;

      final data = snapshot.docs.first.data();
      data['id'] = snapshot.docs.first.id;
      return Promotion.fromJson(data);
    } catch (e) {
      debugPrint('Error fetching promotion by code: $e');
      throw Exception('Failed to fetch promotion by code: $e');
    }
  }

  // Record promotion usage
  Future<void> recordPromotionUsage(
    String promotionId,
    String orderId,
    String customerId,
    double discountAmount,
  ) async {
    try {
      final batch = _firestore.batch();

      // Add usage record
      final usageRef = _firestore.collection(_promotionUsageCollection).doc();
      batch.set(usageRef, {
        'promotionId': promotionId,
        'orderId': orderId,
        'customerId': customerId,
        'discountAmount': discountAmount,
        'usedAt': FieldValue.serverTimestamp(),
      });

      // Increment usage count
      final promotionRef = _firestore.collection(_promotionsCollection).doc(promotionId);
      batch.update(promotionRef, {
        'usageCount': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();
    } catch (e) {
      debugPrint('Error recording promotion usage: $e');
      throw Exception('Failed to record promotion usage: $e');
    }
  }

  // Get promotion usage analytics
  Future<Map<String, dynamic>> getPromotionAnalytics(
    String promotionId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _firestore
          .collection(_promotionUsageCollection)
          .where('promotionId', isEqualTo: promotionId);

      if (startDate != null) {
        query = query.where('usedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('usedAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query.get();
      
      double totalDiscountGiven = 0.0;
      final uniqueCustomers = <String>{};
      
      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        totalDiscountGiven += (data['discountAmount'] as num?)?.toDouble() ?? 0.0;
        uniqueCustomers.add(data['customerId'] as String);
      }

      return {
        'totalUsage': snapshot.docs.length,
        'totalDiscountGiven': totalDiscountGiven,
        'uniqueCustomers': uniqueCustomers.length,
        'averageDiscountPerUse': snapshot.docs.isNotEmpty 
            ? totalDiscountGiven / snapshot.docs.length 
            : 0.0,
      };
    } catch (e) {
      debugPrint('Error fetching promotion analytics: $e');
      throw Exception('Failed to fetch promotion analytics: $e');
    }
  }

  // Get promotions summary for restaurant
  Future<Map<String, dynamic>> getPromotionsSummary(String restaurantId) async {
    try {
      final snapshot = await _firestore
          .collection(_promotionsCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .get();

      int activePromotions = 0;
      int expiredPromotions = 0;
      int totalUsage = 0;
      final now = DateTime.now();

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final isActive = data['isActive'] as bool? ?? false;
        final endDate = (data['endDate'] as Timestamp?)?.toDate();
        
        if (isActive && (endDate == null || endDate.isAfter(now))) {
          activePromotions++;
        } else {
          expiredPromotions++;
        }
        
        totalUsage += (data['usageCount'] as int? ?? 0);
      }

      return {
        'totalPromotions': snapshot.docs.length,
        'activePromotions': activePromotions,
        'expiredPromotions': expiredPromotions,
        'totalUsage': totalUsage,
      };
    } catch (e) {
      debugPrint('Error fetching promotions summary: $e');
      throw Exception('Failed to fetch promotions summary: $e');
    }
  }

  // Generate promo code
  String _generatePromoCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(8, (index) => chars[random % chars.length]).join();
  }
}
