import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:supplier_app/data/models/user.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';
import 'package:supplier_app/core/services/firebase_auth_service.dart';
import 'package:supplier_app/core/services/connectivity_service.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository authRepository;
  final FirebaseAuthService _firebaseAuthService;
  final ConnectivityService _connectivityService;
  StreamSubscription<firebase_auth.User?>? _authStateSubscription;
  StreamSubscription<bool>? _connectivitySubscription;

  AuthBloc({
    required this.authRepository,
    ConnectivityService? connectivityService,
  })  : _firebaseAuthService = FirebaseAuthService(),
        _connectivityService = connectivityService ?? ConnectivityService(),
        super(const AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthRegisterRequested>(_onAuthRegisterRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthTokenRefreshRequested>(_onAuthTokenRefreshRequested);
    on<AuthUserUpdated>(_onAuthUserUpdated);

    // Initialize connectivity service
    _initializeConnectivity();

    // Listen to Firebase auth state changes
    _authStateSubscription = _firebaseAuthService.authStateChanges.listen(
      (firebase_auth.User? user) {
        if (user == null && state is AuthAuthenticated) {
          // Only logout if we were previously authenticated
          add(const AuthLogoutRequested());
        }
      },
    );

    // Listen to connectivity changes
    _connectivitySubscription = _connectivityService.connectivityStream.listen(
      (bool isConnected) {
        if (isConnected && state is AuthError) {
          // Retry authentication check when connection is restored
          add(const AuthCheckRequested());
        }
      },
    );
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    _connectivitySubscription?.cancel();
    _connectivityService.dispose();
    return super.close();
  }

  // Initialize connectivity service (non-blocking)
  void _initializeConnectivity() {
    // Initialize connectivity in background, don't block authentication
    Future.microtask(() async {
      try {
        await _connectivityService.initialize();
      } catch (error) {
        // Silently handle connectivity initialization errors
      }
    });
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    debugPrint('🔍 AuthBloc: Starting authentication check...');
    emit(const AuthLoading());

    // Emergency timeout - if nothing happens in 3 seconds, go to login
    Timer(const Duration(seconds: 3), () {
      if (state is AuthLoading) {
        debugPrint(
            '🆘 AuthBloc: Emergency timeout - forcing unauthenticated state');
        emit(const AuthUnauthenticated());
      }
    });

    try {
      debugPrint('🔍 AuthBloc: Checking if user is authenticated...');
      // Add timeout to prevent hanging
      final isAuthenticated = await authRepository.isAuthenticated().timeout(
        const Duration(seconds: 2),
        onTimeout: () {
          debugPrint('⏰ AuthBloc: Authentication check timed out');
          return false;
        },
      );

      debugPrint('🔍 AuthBloc: Authentication result: $isAuthenticated');

      if (isAuthenticated) {
        debugPrint('🔍 AuthBloc: User is authenticated, getting user data...');
        // Get user data with timeout
        final userFuture = authRepository.getStoredUser().timeout(
          const Duration(seconds: 2),
          onTimeout: () {
            debugPrint('⏰ AuthBloc: User data fetch timed out');
            return null;
          },
        );
        final restaurantIdFuture =
            authRepository.getStoredRestaurantId().timeout(
          const Duration(seconds: 2),
          onTimeout: () {
            debugPrint('⏰ AuthBloc: Restaurant ID fetch timed out');
            return null;
          },
        );

        final results = await Future.wait([userFuture, restaurantIdFuture]);
        final user = results[0] as User?;
        final restaurantId = results[1] as String?;

        debugPrint(
            '🔍 AuthBloc: User: ${user?.email}, Restaurant: $restaurantId');

        if (user != null && restaurantId != null) {
          debugPrint('✅ AuthBloc: Emitting AuthAuthenticated');
          emit(AuthAuthenticated(user: user, restaurantId: restaurantId));
        } else {
          debugPrint(
              '❌ AuthBloc: Missing user data, emitting AuthUnauthenticated');
          emit(const AuthUnauthenticated());
        }
      } else {
        debugPrint(
            '❌ AuthBloc: User not authenticated, emitting AuthUnauthenticated');
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      debugPrint('❌ AuthBloc: Authentication check failed: $e');
      // If authentication check fails, default to unauthenticated
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final loginRequest = LoginRequest(
        email: event.email,
        password: event.password,
        rememberMe: event.rememberMe,
      );

      final loginResponse = await authRepository.login(loginRequest);

      emit(AuthAuthenticated(
        user: loginResponse.user,
        restaurantId: loginResponse.restaurantId,
      ));
    } on AuthException catch (e) {
      emit(AuthError(e.message));
    } catch (e) {
      emit(AuthError('Login failed: ${e.toString()}'));
    }
  }

  Future<void> _onAuthRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      // Register user with Firebase
      final user = await _firebaseAuthService.registerSupplier(
        email: event.email,
        password: event.password,
        restaurantName: event.restaurantName,
        ownerName: event.name,
        phoneNumber: event.phoneNumber,
      );

      // Auth data will be stored automatically by Firebase

      emit(AuthAuthenticated(
        user: user,
        restaurantId: user.id,
      ));
    } on firebase_auth.FirebaseAuthException catch (e) {
      emit(AuthError(e.message ?? 'Registration failed'));
    } catch (e) {
      emit(AuthError('Registration failed: ${e.toString()}'));
    }
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      // Clear remember me preference if explicitly logging out
      final clearRememberMe = event.clearRememberMe ?? true;
      await authRepository.logout(clearRememberMe: clearRememberMe);
      emit(const AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, we should still mark as unauthenticated
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onAuthTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final token = await authRepository.getStoredToken();
      if (token != null) {
        await authRepository.refreshToken(token.refreshToken);

        // Get updated user data
        final user = await authRepository.getCurrentUser();
        final restaurantId = await authRepository.getStoredRestaurantId();

        if (user != null && restaurantId != null) {
          emit(AuthAuthenticated(user: user, restaurantId: restaurantId));
        } else {
          emit(const AuthUnauthenticated());
        }
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onAuthUserUpdated(
    AuthUserUpdated event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      final currentState = state as AuthAuthenticated;
      emit(AuthAuthenticated(
        user: event.user,
        restaurantId: currentState.restaurantId,
      ));
    }
  }
}
