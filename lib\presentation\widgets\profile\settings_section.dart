import 'package:flutter/material.dart';

class SettingsSection extends StatelessWidget {
  final VoidCallback? onNotificationSettings;
  final VoidCallback? onPrivacyPolicy;
  final VoidCallback? onTermsOfService;
  final VoidCallback? onHelp;
  final VoidCallback? onLogout;

  const SettingsSection({
    super.key,
    this.onNotificationSettings,
    this.onPrivacyPolicy,
    this.onTermsOfService,
    this.onHelp,
    this.onLogout,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Settings Items
            _buildSettingsItem(
              context,
              'Notification Settings',
              'Manage your notification preferences',
              Icons.notifications_outlined,
              onNotificationSettings,
            ),
            _buildDivider(),
            _buildSettingsItem(
              context,
              'Privacy Policy',
              'Read our privacy policy',
              Icons.privacy_tip_outlined,
              onPrivacyPolicy,
            ),
            _buildDivider(),
            _buildSettingsItem(
              context,
              'Terms of Service',
              'Read our terms of service',
              Icons.description_outlined,
              onTermsOfService,
            ),
            _buildDivider(),
            _buildSettingsItem(
              context,
              'Help & Support',
              'Get help and contact support',
              Icons.help_outline,
              onHelp,
            ),
            _buildDivider(),
            _buildSettingsItem(
              context,
              'Logout',
              'Sign out of your account',
              Icons.logout,
              onLogout,
              isDestructive: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback? onTap, {
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDestructive 
                    ? Colors.red.withValues(alpha: 0.1)
                    : Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isDestructive 
                    ? Colors.red 
                    : Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isDestructive ? Colors.red : null,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Divider(
        color: Colors.grey[200],
        height: 1,
      ),
    );
  }
}
