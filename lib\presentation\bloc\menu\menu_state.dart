import 'package:equatable/equatable.dart';
import 'package:supplier_app/data/models/menu.dart';

abstract class MenuState extends Equatable {
  const MenuState();

  @override
  List<Object?> get props => [];
}

class MenuInitial extends MenuState {
  const MenuInitial();
}

class MenuLoading extends MenuState {
  const MenuLoading();
}

class MenuLoaded extends MenuState {
  final List<MenuCategory> categories;
  final List<Dish> dishes;
  final String? selectedCategoryId;
  final bool isLoadingCategories;
  final bool isLoadingDishes;

  const MenuLoaded({
    required this.categories,
    required this.dishes,
    this.selectedCategoryId,
    this.isLoadingCategories = false,
    this.isLoadingDishes = false,
  });

  @override
  List<Object?> get props => [
        categories,
        dishes,
        selectedCategoryId,
        isLoadingCategories,
        isLoadingDishes,
      ];

  MenuLoaded copyWith({
    List<MenuCategory>? categories,
    List<Dish>? dishes,
    String? selectedCategoryId,
    bool? isLoadingCategories,
    bool? isLoadingDishes,
  }) {
    return MenuLoaded(
      categories: categories ?? this.categories,
      dishes: dishes ?? this.dishes,
      selectedCategoryId: selectedCategoryId ?? this.selectedCategoryId,
      isLoadingCategories: isLoadingCategories ?? this.isLoadingCategories,
      isLoadingDishes: isLoadingDishes ?? this.isLoadingDishes,
    );
  }

  // Convenience getters
  List<Dish> get filteredDishes {
    if (selectedCategoryId == null) return dishes;
    return dishes.where((dish) => dish.categoryId == selectedCategoryId).toList();
  }

  List<Dish> get availableDishes => dishes.where((dish) => dish.isAvailable).toList();
  List<Dish> get unavailableDishes => dishes.where((dish) => !dish.isAvailable).toList();
  List<Dish> get popularDishes => dishes.where((dish) => dish.isPopular).toList();
}

class MenuError extends MenuState {
  final String message;

  const MenuError(this.message);

  @override
  List<Object> get props => [message];
}

// Category States
class MenuCategoryCreating extends MenuState {
  const MenuCategoryCreating();
}

class MenuCategoryCreateSuccess extends MenuState {
  final MenuCategory category;

  const MenuCategoryCreateSuccess(this.category);

  @override
  List<Object> get props => [category];
}

class MenuCategoryCreateError extends MenuState {
  final String message;

  const MenuCategoryCreateError(this.message);

  @override
  List<Object> get props => [message];
}

class MenuCategoryUpdating extends MenuState {
  final String categoryId;

  const MenuCategoryUpdating(this.categoryId);

  @override
  List<Object> get props => [categoryId];
}

class MenuCategoryUpdateSuccess extends MenuState {
  final MenuCategory category;

  const MenuCategoryUpdateSuccess(this.category);

  @override
  List<Object> get props => [category];
}

class MenuCategoryUpdateError extends MenuState {
  final String message;

  const MenuCategoryUpdateError(this.message);

  @override
  List<Object> get props => [message];
}

class MenuCategoryDeleting extends MenuState {
  final String categoryId;

  const MenuCategoryDeleting(this.categoryId);

  @override
  List<Object> get props => [categoryId];
}

class MenuCategoryDeleteSuccess extends MenuState {
  final String categoryId;

  const MenuCategoryDeleteSuccess(this.categoryId);

  @override
  List<Object> get props => [categoryId];
}

class MenuCategoryDeleteError extends MenuState {
  final String message;

  const MenuCategoryDeleteError(this.message);

  @override
  List<Object> get props => [message];
}

// Dish States
class MenuDishCreating extends MenuState {
  const MenuDishCreating();
}

class MenuDishCreateSuccess extends MenuState {
  final Dish dish;

  const MenuDishCreateSuccess(this.dish);

  @override
  List<Object> get props => [dish];
}

class MenuDishCreateError extends MenuState {
  final String message;

  const MenuDishCreateError(this.message);

  @override
  List<Object> get props => [message];
}

class MenuDishUpdating extends MenuState {
  final String dishId;

  const MenuDishUpdating(this.dishId);

  @override
  List<Object> get props => [dishId];
}

class MenuDishUpdateSuccess extends MenuState {
  final Dish dish;

  const MenuDishUpdateSuccess(this.dish);

  @override
  List<Object> get props => [dish];
}

class MenuDishUpdateError extends MenuState {
  final String message;

  const MenuDishUpdateError(this.message);

  @override
  List<Object> get props => [message];
}

class MenuDishDeleting extends MenuState {
  final String dishId;

  const MenuDishDeleting(this.dishId);

  @override
  List<Object> get props => [dishId];
}

class MenuDishDeleteSuccess extends MenuState {
  final String dishId;

  const MenuDishDeleteSuccess(this.dishId);

  @override
  List<Object> get props => [dishId];
}

class MenuDishDeleteError extends MenuState {
  final String message;

  const MenuDishDeleteError(this.message);

  @override
  List<Object> get props => [message];
}

// Image Upload States
class MenuImageUploading extends MenuState {
  const MenuImageUploading();
}

class MenuImageUploadSuccess extends MenuState {
  final String imageUrl;

  const MenuImageUploadSuccess(this.imageUrl);

  @override
  List<Object> get props => [imageUrl];
}

class MenuImageUploadError extends MenuState {
  final String message;

  const MenuImageUploadError(this.message);

  @override
  List<Object> get props => [message];
}
