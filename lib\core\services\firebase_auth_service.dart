import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:supplier_app/data/models/user.dart' as app_user;

class FirebaseAuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _firebaseAuth.currentUser;

  // Auth state stream
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // Sign in with email and password
  Future<app_user.User> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result =
          await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = result.user;
      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-null',
          message: 'User is null after sign in',
        );
      }

      // Try to get user data from Firestore
      try {
        final userData = await _getUserData(user.uid);

        // Verify user is a supplier/restaurant owner
        if (userData['role'] != 'supplier') {
          await signOut(); // Sign out if not a supplier
          throw FirebaseAuthException(
            code: 'unauthorized-role',
            message: 'This account is not authorized for supplier access',
          );
        }

        return app_user.User.fromFirestore(userData, user.uid);
      } catch (e) {
        // If user document doesn't exist, create it automatically
        debugPrint(
            'User document not found, creating default supplier document...');
        await _createDefaultSupplierDocument(user);

        // Get the newly created user data
        final userData = await _getUserData(user.uid);
        return app_user.User.fromFirestore(userData, user.uid);
      }
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw FirebaseAuthException(
        code: 'unknown-error',
        message: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  // Register new supplier account
  Future<app_user.User> registerSupplier({
    required String email,
    required String password,
    required String restaurantName,
    required String ownerName,
    required String phoneNumber,
  }) async {
    try {
      final UserCredential result =
          await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = result.user;
      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-null',
          message: 'User is null after registration',
        );
      }

      // Create user document in Firestore
      final userData = {
        'email': email,
        'name': ownerName,
        'phoneNumber': phoneNumber,
        'role': 'supplier',
        'restaurantName': restaurantName,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('users').doc(user.uid).set(userData);

      // Create restaurant document
      await _createRestaurantDocument(user.uid, restaurantName, ownerName);

      return app_user.User.fromFirestore(userData, user.uid);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw FirebaseAuthException(
        code: 'unknown-error',
        message: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      throw FirebaseAuthException(
        code: 'sign-out-error',
        message: 'Failed to sign out: ${e.toString()}',
      );
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw FirebaseAuthException(
        code: 'unknown-error',
        message: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  // Get user data from Firestore
  Future<Map<String, dynamic>> _getUserData(String uid) async {
    try {
      final DocumentSnapshot doc =
          await _firestore.collection('users').doc(uid).get();

      if (!doc.exists) {
        throw FirebaseAuthException(
          code: 'user-not-found',
          message: 'User data not found in database',
        );
      }

      return doc.data() as Map<String, dynamic>;
    } catch (e) {
      if (e is FirebaseAuthException) rethrow;
      throw FirebaseAuthException(
        code: 'database-error',
        message: 'Failed to fetch user data: ${e.toString()}',
      );
    }
  }

  // Create restaurant document
  Future<void> _createRestaurantDocument(
    String userId,
    String restaurantName,
    String ownerName,
  ) async {
    try {
      final restaurantData = {
        'name': restaurantName,
        'ownerId': userId,
        'ownerName': ownerName,
        'status': 'offline',
        'isActive': true,
        'rating': 0.0,
        'totalOrders': 0,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'businessHours': {
          'monday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
          'tuesday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
          'wednesday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
          'thursday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
          'friday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
          'saturday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
          'sunday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
        },
      };

      await _firestore
          .collection('restaurants')
          .doc(userId)
          .set(restaurantData);
    } catch (e) {
      throw FirebaseAuthException(
        code: 'database-error',
        message: 'Failed to create restaurant profile: ${e.toString()}',
      );
    }
  }

  // Create default supplier document for existing Firebase Auth users
  Future<void> _createDefaultSupplierDocument(User user) async {
    try {
      final userData = {
        'email': user.email ?? '',
        'name': user.displayName ?? 'Supplier',
        'phone': user.phoneNumber ?? '',
        'role': 'supplier',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('users').doc(user.uid).set(userData);

      // Create default restaurant document
      await _createRestaurantDocument(
        user.uid,
        'My Restaurant', // Default restaurant name
        user.displayName ?? 'Owner',
      );
    } catch (e) {
      throw FirebaseAuthException(
        code: 'database-error',
        message: 'Failed to create user profile: ${e.toString()}',
      );
    }
  }

  // Handle Firebase Auth exceptions
  FirebaseAuthException _handleAuthException(FirebaseAuthException e) {
    String message;

    switch (e.code) {
      case 'user-not-found':
        message = 'No account found with this email address.';
        break;
      case 'wrong-password':
        message = 'Incorrect password. Please try again.';
        break;
      case 'email-already-in-use':
        message = 'An account already exists with this email address.';
        break;
      case 'weak-password':
        message = 'Password is too weak. Please choose a stronger password.';
        break;
      case 'invalid-email':
        message = 'Please enter a valid email address.';
        break;
      case 'user-disabled':
        message = 'This account has been disabled. Please contact support.';
        break;
      case 'too-many-requests':
        message = 'Too many failed attempts. Please try again later.';
        break;
      case 'network-request-failed':
        message = 'Network error. Please check your internet connection.';
        break;
      case 'unauthorized-role':
        message = 'This account is not authorized for supplier access.';
        break;
      default:
        message = e.message ?? 'An authentication error occurred.';
    }

    return FirebaseAuthException(code: e.code, message: message);
  }

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Get current user ID
  String? get currentUserId => currentUser?.uid;

  // Get current user email
  String? get currentUserEmail => currentUser?.email;

  // Update user profile
  Future<void> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      final user = currentUser;
      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-not-authenticated',
          message: 'User is not authenticated',
        );
      }

      await user.updateDisplayName(displayName);
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw FirebaseAuthException(
        code: 'unknown-error',
        message: 'Failed to update profile: ${e.toString()}',
      );
    }
  }

  // Reload user data
  Future<void> reloadUser() async {
    try {
      await currentUser?.reload();
    } catch (e) {
      throw FirebaseAuthException(
        code: 'reload-error',
        message: 'Failed to reload user data: ${e.toString()}',
      );
    }
  }
}
