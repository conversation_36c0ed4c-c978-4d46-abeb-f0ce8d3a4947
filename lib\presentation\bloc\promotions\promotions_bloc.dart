import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/data/models/promotion.dart';
import 'package:supplier_app/data/repositories/promotion_repository.dart';
import 'promotions_event.dart';
import 'promotions_state.dart';

class PromotionsBloc extends Bloc<PromotionsEvent, PromotionsState> {
  final PromotionRepository promotionRepository;

  PromotionsBloc({required this.promotionRepository})
      : super(const PromotionsInitial()) {
    on<PromotionsLoadRequested>(_onPromotionsLoadRequested);
    on<PromotionDetailsRequested>(_onPromotionDetailsRequested);
    on<PromotionCreateRequested>(_onPromotionCreateRequested);
    on<PromotionUpdateRequested>(_onPromotionUpdateRequested);
    on<PromotionDeleteRequested>(_onPromotionDeleteRequested);
    on<PromotionStatusToggleRequested>(_onPromotionStatusToggleRequested);
    on<PromotionUsageRequested>(_onPromotionUsageRequested);
    on<ActivePromotionsRequested>(_onActivePromotionsRequested);
    on<PromotionsFilterChanged>(_onPromotionsFilterChanged);
  }

  Future<void> _onPromotionsLoadRequested(
    PromotionsLoadRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    if (event.isRefresh && state is PromotionsLoaded) {
      final currentState = state as PromotionsLoaded;
      emit(currentState.copyWith(isRefreshing: true));
    } else {
      emit(const PromotionsLoading());
    }

    try {
      final promotions = await promotionRepository.getPromotions(
        isActive: event.isActive,
      );

      emit(PromotionsLoaded(
        promotions: promotions,
        currentFilter: event.isActive,
      ));
    } catch (e) {
      emit(PromotionsError(e.toString()));
    }
  }

  Future<void> _onPromotionDetailsRequested(
    PromotionDetailsRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(PromotionDetailsLoading(event.promotionId));

    try {
      final promotion =
          await promotionRepository.getPromotionById(event.promotionId);
      if (promotion != null) {
        emit(PromotionDetailsLoaded(promotion));
      } else {
        emit(PromotionDetailsError('Promotion not found', event.promotionId));
      }
    } catch (e) {
      emit(PromotionDetailsError(e.toString(), event.promotionId));
    }
  }

  Future<void> _onPromotionCreateRequested(
    PromotionCreateRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(const PromotionCreating());

    try {
      final createdPromotion =
          await promotionRepository.createPromotion(event.promotion);
      emit(PromotionCreateSuccess(createdPromotion));

      // Add promotion to the list
      if (state is PromotionsLoaded) {
        _addPromotionToList(createdPromotion, emit);
      }
    } catch (e) {
      emit(PromotionCreateError(e.toString()));
    }
  }

  Future<void> _onPromotionUpdateRequested(
    PromotionUpdateRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(PromotionUpdating(event.promotion.id));

    try {
      final updatedPromotion =
          await promotionRepository.updatePromotion(event.promotion);
      emit(PromotionUpdateSuccess(updatedPromotion));

      // Update promotion in the list
      if (state is PromotionsLoaded) {
        _updatePromotionInList(updatedPromotion, emit);
      }
    } catch (e) {
      emit(PromotionUpdateError(e.toString()));
    }
  }

  Future<void> _onPromotionDeleteRequested(
    PromotionDeleteRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(PromotionDeleting(event.promotionId));

    try {
      await promotionRepository.deletePromotion(event.promotionId);
      emit(PromotionDeleteSuccess(event.promotionId));

      // Remove promotion from the list
      if (state is PromotionsLoaded) {
        _removePromotionFromList(event.promotionId, emit);
      }
    } catch (e) {
      emit(PromotionDeleteError(e.toString()));
    }
  }

  Future<void> _onPromotionStatusToggleRequested(
    PromotionStatusToggleRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(PromotionUpdating(event.promotionId));

    try {
      final updatedPromotion = await promotionRepository.togglePromotionStatus(
        event.promotionId,
        event.isActive,
      );
      emit(PromotionUpdateSuccess(updatedPromotion));

      // Update promotion in the list
      if (state is PromotionsLoaded) {
        _updatePromotionInList(updatedPromotion, emit);
      }
    } catch (e) {
      emit(PromotionUpdateError(e.toString()));
    }
  }

  Future<void> _onPromotionUsageRequested(
    PromotionUsageRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(PromotionUsageLoading(event.promotionId));

    try {
      final usage = await promotionRepository.getPromotionUsage(
        event.promotionId,
        startDate: event.startDate,
        endDate: event.endDate,
      );
      emit(PromotionUsageLoaded(event.promotionId, usage));
    } catch (e) {
      emit(PromotionUsageError(e.toString(), event.promotionId));
    }
  }

  Future<void> _onActivePromotionsRequested(
    ActivePromotionsRequested event,
    Emitter<PromotionsState> emit,
  ) async {
    emit(const ActivePromotionsLoading());

    try {
      final activePromotions = await promotionRepository.getActivePromotions();
      emit(ActivePromotionsLoaded(activePromotions));
    } catch (e) {
      emit(ActivePromotionsError(e.toString()));
    }
  }

  Future<void> _onPromotionsFilterChanged(
    PromotionsFilterChanged event,
    Emitter<PromotionsState> emit,
  ) async {
    // Reload promotions with new filter
    add(PromotionsLoadRequested(isActive: event.isActive));
  }

  // Helper methods
  void _addPromotionToList(
      Promotion newPromotion, Emitter<PromotionsState> emit) {
    if (state is PromotionsLoaded) {
      final currentState = state as PromotionsLoaded;
      final updatedPromotions = [...currentState.promotions, newPromotion];

      emit(currentState.copyWith(promotions: updatedPromotions));
    }
  }

  void _updatePromotionInList(
      Promotion updatedPromotion, Emitter<PromotionsState> emit) {
    if (state is PromotionsLoaded) {
      final currentState = state as PromotionsLoaded;
      final updatedPromotions = currentState.promotions.map((promotion) {
        return promotion.id == updatedPromotion.id
            ? updatedPromotion
            : promotion;
      }).toList();

      emit(currentState.copyWith(promotions: updatedPromotions));
    }
  }

  void _removePromotionFromList(
      String promotionId, Emitter<PromotionsState> emit) {
    if (state is PromotionsLoaded) {
      final currentState = state as PromotionsLoaded;
      final updatedPromotions = currentState.promotions
          .where((promotion) => promotion.id != promotionId)
          .toList();

      emit(currentState.copyWith(promotions: updatedPromotions));
    }
  }
}
