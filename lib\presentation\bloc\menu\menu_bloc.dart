import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/data/models/menu.dart';
import 'package:supplier_app/data/repositories/menu_repository.dart';
import 'menu_event.dart';
import 'menu_state.dart';

class MenuBloc extends Bloc<MenuEvent, MenuState> {
  final MenuRepository menuRepository;

  MenuBloc({required this.menuRepository}) : super(const MenuInitial()) {
    on<MenuCategoriesLoadRequested>(_onMenuCategoriesLoadRequested);
    on<MenuCategoryCreateRequested>(_onMenuCategoryCreateRequested);
    on<MenuCategoryUpdateRequested>(_onMenuCategoryUpdateRequested);
    on<MenuCategoryDeleteRequested>(_onMenuCategoryDeleteRequested);
    on<MenuDishesLoadRequested>(_onMenuDishesLoadRequested);
    on<MenuDishCreateRequested>(_onMenuDishCreateRequested);
    on<MenuDishUpdateRequested>(_onMenuDishUpdateRequested);
    on<MenuDishDeleteRequested>(_onMenuDishDeleteRequested);
    on<MenuDishAvailabilityToggleRequested>(
        _onMenuDishAvailabilityToggleRequested);
    on<MenuImageUploadRequested>(_onMenuImageUploadRequested);
    on<MenuFilterChanged>(_onMenuFilterChanged);
  }

  Future<void> _onMenuCategoriesLoadRequested(
    MenuCategoriesLoadRequested event,
    Emitter<MenuState> emit,
  ) async {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      emit(currentState.copyWith(isLoadingCategories: true));
    } else {
      emit(const MenuLoading());
    }

    try {
      final categories = await menuRepository.getCategories();

      if (state is MenuLoaded) {
        final currentState = state as MenuLoaded;
        emit(currentState.copyWith(
          categories: categories,
          isLoadingCategories: false,
        ));
      } else {
        emit(MenuLoaded(
          categories: categories,
          dishes: const [],
        ));
      }
    } catch (e) {
      emit(MenuError(e.toString()));
    }
  }

  Future<void> _onMenuCategoryCreateRequested(
    MenuCategoryCreateRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(const MenuCategoryCreating());

    try {
      // Create category first
      final createdCategory =
          await menuRepository.createCategory(event.category);

      // Upload image if provided
      if (event.imageFile != null) {
        final imageUrl = await menuRepository.uploadImage(
          event.imageFile!,
          categoryId: createdCategory.id,
        );

        // Update category with image URL
        final updatedCategory = createdCategory.copyWith(imageUrl: imageUrl);
        await menuRepository.updateCategory(updatedCategory);

        emit(MenuCategoryCreateSuccess(updatedCategory));
      } else {
        emit(MenuCategoryCreateSuccess(createdCategory));
      }

      // Reload categories
      add(const MenuCategoriesLoadRequested());
    } catch (e) {
      emit(MenuError(e.toString()));
    }
  }

  Future<void> _onMenuCategoryUpdateRequested(
    MenuCategoryUpdateRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(MenuCategoryUpdating(event.category.id));

    try {
      final updatedCategory =
          await menuRepository.updateCategory(event.category);
      emit(MenuCategoryUpdateSuccess(updatedCategory));

      // Update category in the list
      if (state is MenuLoaded) {
        _updateCategoryInList(updatedCategory, emit);
      }
    } catch (e) {
      emit(MenuCategoryUpdateError(e.toString()));
    }
  }

  Future<void> _onMenuCategoryDeleteRequested(
    MenuCategoryDeleteRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(MenuCategoryDeleting(event.categoryId));

    try {
      await menuRepository.deleteCategory(event.categoryId);
      emit(MenuCategoryDeleteSuccess(event.categoryId));

      // Remove category from the list
      if (state is MenuLoaded) {
        _removeCategoryFromList(event.categoryId, emit);
      }
    } catch (e) {
      emit(MenuCategoryDeleteError(e.toString()));
    }
  }

  Future<void> _onMenuDishesLoadRequested(
    MenuDishesLoadRequested event,
    Emitter<MenuState> emit,
  ) async {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      emit(currentState.copyWith(isLoadingDishes: true));
    } else {
      emit(const MenuLoading());
    }

    try {
      final dishes =
          await menuRepository.getDishes(categoryId: event.categoryId);

      if (state is MenuLoaded) {
        final currentState = state as MenuLoaded;
        emit(currentState.copyWith(
          dishes: dishes,
          selectedCategoryId: event.categoryId,
          isLoadingDishes: false,
        ));
      } else {
        emit(MenuLoaded(
          categories: const [],
          dishes: dishes,
          selectedCategoryId: event.categoryId,
        ));
      }
    } catch (e) {
      emit(MenuError(e.toString()));
    }
  }

  Future<void> _onMenuDishCreateRequested(
    MenuDishCreateRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(const MenuDishCreating());

    try {
      // Create dish first
      final createdDish = await menuRepository.createDish(event.dish);

      // Upload image if provided
      if (event.imageFile != null) {
        final imageUrl = await menuRepository.uploadImage(
          event.imageFile!,
          dishId: createdDish.id,
        );

        // Update dish with image URL
        final updatedDish = createdDish.copyWith(imageUrl: imageUrl);
        await menuRepository.updateDish(updatedDish);

        emit(MenuDishCreateSuccess(updatedDish));

        // Add updated dish to the list
        if (state is MenuLoaded) {
          _addDishToList(updatedDish, emit);
        }
      } else {
        emit(MenuDishCreateSuccess(createdDish));

        // Add dish to the list
        if (state is MenuLoaded) {
          _addDishToList(createdDish, emit);
        }
      }
    } catch (e) {
      emit(MenuError(e.toString()));
    }
  }

  Future<void> _onMenuDishUpdateRequested(
    MenuDishUpdateRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(MenuDishUpdating(event.dish.id));

    try {
      final updatedDish = await menuRepository.updateDish(event.dish);
      emit(MenuDishUpdateSuccess(updatedDish));

      // Update dish in the list
      if (state is MenuLoaded) {
        _updateDishInList(updatedDish, emit);
      }
    } catch (e) {
      emit(MenuDishUpdateError(e.toString()));
    }
  }

  Future<void> _onMenuDishDeleteRequested(
    MenuDishDeleteRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(MenuDishDeleting(event.dishId));

    try {
      await menuRepository.deleteDish(event.dishId);
      emit(MenuDishDeleteSuccess(event.dishId));

      // Remove dish from the list
      if (state is MenuLoaded) {
        _removeDishFromList(event.dishId, emit);
      }
    } catch (e) {
      emit(MenuDishDeleteError(e.toString()));
    }
  }

  Future<void> _onMenuDishAvailabilityToggleRequested(
    MenuDishAvailabilityToggleRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(MenuDishUpdating(event.dishId));

    try {
      final updatedDish = await menuRepository.toggleDishAvailability(
        event.dishId,
        event.isAvailable,
      );
      emit(MenuDishUpdateSuccess(updatedDish));

      // Update dish in the list
      if (state is MenuLoaded) {
        _updateDishInList(updatedDish, emit);
      }
    } catch (e) {
      emit(MenuDishUpdateError(e.toString()));
    }
  }

  Future<void> _onMenuImageUploadRequested(
    MenuImageUploadRequested event,
    Emitter<MenuState> emit,
  ) async {
    emit(const MenuImageUploading());

    try {
      final imageUrl = await menuRepository.uploadImage(event.imageFile);
      emit(MenuImageUploadSuccess(imageUrl));
    } catch (e) {
      emit(MenuImageUploadError(e.toString()));
    }
  }

  Future<void> _onMenuFilterChanged(
    MenuFilterChanged event,
    Emitter<MenuState> emit,
  ) async {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      emit(currentState.copyWith(selectedCategoryId: event.categoryId));
    }
  }

  // Helper methods
  void _updateCategoryInList(
      MenuCategory updatedCategory, Emitter<MenuState> emit) {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      final updatedCategories = currentState.categories.map((category) {
        return category.id == updatedCategory.id ? updatedCategory : category;
      }).toList();

      emit(currentState.copyWith(categories: updatedCategories));
    }
  }

  void _removeCategoryFromList(String categoryId, Emitter<MenuState> emit) {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      final updatedCategories = currentState.categories
          .where((category) => category.id != categoryId)
          .toList();

      emit(currentState.copyWith(categories: updatedCategories));
    }
  }

  void _addDishToList(Dish newDish, Emitter<MenuState> emit) {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      final updatedDishes = [...currentState.dishes, newDish];

      emit(currentState.copyWith(dishes: updatedDishes));
    }
  }

  void _updateDishInList(Dish updatedDish, Emitter<MenuState> emit) {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      final updatedDishes = currentState.dishes.map((dish) {
        return dish.id == updatedDish.id ? updatedDish : dish;
      }).toList();

      emit(currentState.copyWith(dishes: updatedDishes));
    }
  }

  void _removeDishFromList(String dishId, Emitter<MenuState> emit) {
    if (state is MenuLoaded) {
      final currentState = state as MenuLoaded;
      final updatedDishes =
          currentState.dishes.where((dish) => dish.id != dishId).toList();

      emit(currentState.copyWith(dishes: updatedDishes));
    }
  }
}
