import 'package:flutter/material.dart';

class PerformanceChartCard extends StatelessWidget {
  final List<double> weeklyData;
  final String title;
  final String subtitle;

  const PerformanceChartCard({
    super.key,
    required this.weeklyData,
    this.title = 'Weekly Performance',
    this.subtitle = 'Orders this week',
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 140, // Increased height to accommodate content
              child: _buildSimpleChart(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleChart(BuildContext context) {
    if (weeklyData.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      );
    }

    final maxValue = weeklyData.reduce((a, b) => a > b ? a : b);
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: List.generate(weeklyData.length, (index) {
        final value = weeklyData[index];
        final height = maxValue > 0 ? (value / maxValue) * 100 : 0.0;

        return Flexible(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Value text at top
              Text(
                value.toInt().toString(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 11,
                    ),
              ),
              const SizedBox(height: 2),
              // Chart bar - flexible height
              Expanded(
                child: Container(
                  width: 24,
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      width: 24,
                      height: height.clamp(
                          4.0, double.infinity), // Minimum height of 4
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(3),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 4),
              // Day label at bottom
              Text(
                index < days.length ? days[index] : '',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
