import 'package:equatable/equatable.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

abstract class AnalyticsEvent extends Equatable {
  const AnalyticsEvent();

  @override
  List<Object?> get props => [];
}

class AnalyticsSalesReportRequested extends AnalyticsEvent {
  final ReportType type;
  final DateTime? startDate;
  final DateTime? endDate;

  const AnalyticsSalesReportRequested({
    required this.type,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [type, startDate, endDate];
}

class AnalyticsOrderTrendsRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;

  const AnalyticsOrderTrendsRequested({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object> get props => [startDate, endDate];
}

class AnalyticsPopularDishesRequested extends AnalyticsEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final int limit;

  const AnalyticsPopularDishesRequested({
    this.startDate,
    this.endDate,
    this.limit = 10,
  });

  @override
  List<Object?> get props => [startDate, endDate, limit];
}

class AnalyticsCustomerFeedbackRequested extends AnalyticsEvent {
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minRating;
  final double? maxRating;

  const AnalyticsCustomerFeedbackRequested({
    this.startDate,
    this.endDate,
    this.minRating,
    this.maxRating,
  });

  @override
  List<Object?> get props => [startDate, endDate, minRating, maxRating];
}

class AnalyticsRevenueRequested extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;

  const AnalyticsRevenueRequested({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object> get props => [startDate, endDate];
}

class AnalyticsPerformanceMetricsRequested extends AnalyticsEvent {
  final DateTime? startDate;
  final DateTime? endDate;

  const AnalyticsPerformanceMetricsRequested({
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];
}

class AnalyticsDateRangeChanged extends AnalyticsEvent {
  final DateTime startDate;
  final DateTime endDate;

  const AnalyticsDateRangeChanged({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object> get props => [startDate, endDate];
}

class AnalyticsRefreshRequested extends AnalyticsEvent {
  const AnalyticsRefreshRequested();
}
