import 'package:equatable/equatable.dart';
import 'package:supplier_app/data/models/promotion.dart';

abstract class PromotionsEvent extends Equatable {
  const PromotionsEvent();

  @override
  List<Object?> get props => [];
}

class PromotionsLoadRequested extends PromotionsEvent {
  final bool? isActive;
  final bool isRefresh;

  const PromotionsLoadRequested({
    this.isActive,
    this.isRefresh = false,
  });

  @override
  List<Object?> get props => [isActive, isRefresh];
}

class PromotionDetailsRequested extends PromotionsEvent {
  final String promotionId;

  const PromotionDetailsRequested(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionCreateRequested extends PromotionsEvent {
  final Promotion promotion;

  const PromotionCreateRequested(this.promotion);

  @override
  List<Object> get props => [promotion];
}

class PromotionUpdateRequested extends PromotionsEvent {
  final Promotion promotion;

  const PromotionUpdateRequested(this.promotion);

  @override
  List<Object> get props => [promotion];
}

class PromotionDeleteRequested extends PromotionsEvent {
  final String promotionId;

  const PromotionDeleteRequested(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionStatusToggleRequested extends PromotionsEvent {
  final String promotionId;
  final bool isActive;

  const PromotionStatusToggleRequested(this.promotionId, this.isActive);

  @override
  List<Object> get props => [promotionId, isActive];
}

class PromotionUsageRequested extends PromotionsEvent {
  final String promotionId;
  final DateTime? startDate;
  final DateTime? endDate;

  const PromotionUsageRequested(
    this.promotionId, {
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [promotionId, startDate, endDate];
}

class ActivePromotionsRequested extends PromotionsEvent {
  const ActivePromotionsRequested();
}

class PromotionsFilterChanged extends PromotionsEvent {
  final bool? isActive;

  const PromotionsFilterChanged({this.isActive});

  @override
  List<Object?> get props => [isActive];
}
