import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/theme/app_theme.dart';
import 'package:supplier_app/firebase_options.dart';
import 'package:supplier_app/presentation/bloc/auth/auth_bloc.dart';
import 'package:supplier_app/presentation/bloc/auth/auth_state.dart';
import 'package:supplier_app/presentation/bloc/auth/auth_event.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_bloc.dart';
import 'package:supplier_app/presentation/bloc/orders/orders_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_bloc.dart';
import 'package:supplier_app/presentation/bloc/promotions/promotions_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_bloc.dart';
import 'package:supplier_app/presentation/screens/auth/login_screen.dart';
import 'package:supplier_app/presentation/screens/main/main_screen.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';
import 'package:supplier_app/data/repositories/order_repository.dart';
import 'package:supplier_app/data/repositories/menu_repository.dart';
import 'package:supplier_app/data/repositories/promotion_repository.dart';
import 'package:supplier_app/data/repositories/analytics_repository.dart';

void main() async{
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
);
  runApp(const SupplierApp());
}

class SupplierApp extends StatelessWidget {
  const SupplierApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<AuthRepository>(
          create: (context) => AuthRepository(),
        ),
        RepositoryProvider<OrderRepository>(
          create: (context) => OrderRepository(),
        ),
        RepositoryProvider<MenuRepository>(
          create: (context) => MenuRepository(),
        ),
        RepositoryProvider<PromotionRepository>(
          create: (context) => PromotionRepository(),
        ),
        RepositoryProvider<AnalyticsRepository>(
          create: (context) => AnalyticsRepository(),
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider<AuthBloc>(
            create: (context) => AuthBloc(
              authRepository: context.read<AuthRepository>(),
            ),
          ),
          BlocProvider<DashboardBloc>(
            create: (context) => DashboardBloc(
              orderRepository: context.read<OrderRepository>(),
            ),
          ),
          BlocProvider<OrdersBloc>(
            create: (context) => OrdersBloc(
              orderRepository: context.read<OrderRepository>(),
            ),
          ),
          BlocProvider<MenuBloc>(
            create: (context) => MenuBloc(
              menuRepository: context.read<MenuRepository>(),
            ),
          ),
          BlocProvider<PromotionsBloc>(
            create: (context) => PromotionsBloc(
              promotionRepository: context.read<PromotionRepository>(),
            ),
          ),
          BlocProvider<AnalyticsBloc>(
            create: (context) => AnalyticsBloc(
              analyticsRepository: context.read<AnalyticsRepository>(),
            ),
          ),
        ],
        child: MaterialApp(
          title: 'Supplier App',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          home: BlocConsumer<AuthBloc, AuthState>(
            listener: (context, state) {
              // Initialize auth check when app starts
              if (state is AuthInitial) {
                context.read<AuthBloc>().add(const AuthCheckRequested());
              }
            },
            builder: (context, state) {
              if (state is AuthAuthenticated) {
                return const MainScreen();
              }
              return const LoginScreen();
            },
          ),
          debugShowCheckedModeBanner: false,
        ),
      ),
    );
  }
}
