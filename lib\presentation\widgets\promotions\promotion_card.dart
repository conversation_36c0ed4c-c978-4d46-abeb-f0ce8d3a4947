import 'package:flutter/material.dart';
import 'package:supplier_app/data/models/promotion.dart';
import 'package:supplier_app/core/utils/date_utils.dart';

class PromotionCard extends StatelessWidget {
  final Promotion promotion;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final Function(bool)? onToggleStatus;
  final VoidCallback? onViewUsage;

  const PromotionCard({
    super.key,
    required this.promotion,
    this.onEdit,
    this.onDelete,
    this.onToggleStatus,
    this.onViewUsage,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and status
            _buildHeader(context),
            const SizedBox(height: 12),

            // Description
            Text(
              promotion.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),

            // Promotion details
            _buildPromotionDetails(context),
            const SizedBox(height: 12),

            // Date range
            _buildDateRange(context),
            const SizedBox(height: 16),

            // Actions
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                promotion.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                promotion.displayValue,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            _getStatusText(),
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPromotionDetails(BuildContext context) {
    return Row(
      children: [
        if (promotion.minimumOrderAmount != null) ...[
          Icon(
            Icons.shopping_cart_outlined,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            'Min: \$${promotion.minimumOrderAmount!.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(width: 16),
        ],
        if (promotion.hasUsageLimit) ...[
          Icon(
            Icons.people_outline,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            '${promotion.usageCount}/${promotion.usageLimit} used',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
        if (promotion.promoCode != null) ...[
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              promotion.promoCode!,
              style: const TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDateRange(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.calendar_today_outlined,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          '${AppDateUtils.formatDisplayDate(promotion.startDate)} - ${AppDateUtils.formatDisplayDate(promotion.endDate)}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        const Spacer(),
        if (promotion.isExpired)
          Text(
            'Expired',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          )
        else if (promotion.isNotStarted)
          Text(
            'Upcoming',
            style: TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      children: [
        if (onViewUsage != null)
          TextButton.icon(
            onPressed: onViewUsage,
            icon: const Icon(Icons.analytics_outlined, size: 16),
            label: const Text('Usage'),
          ),
        const Spacer(),
        if (onEdit != null)
          IconButton(
            onPressed: onEdit,
            icon: const Icon(Icons.edit_outlined),
            tooltip: 'Edit Promotion',
            iconSize: 20,
          ),
        if (onToggleStatus != null && !promotion.isExpired)
          IconButton(
            onPressed: () => onToggleStatus!(!promotion.isActive),
            icon: Icon(
              promotion.isActive
                  ? Icons.pause_circle_outline
                  : Icons.play_circle_outline,
            ),
            tooltip: promotion.isActive ? 'Deactivate' : 'Activate',
            iconSize: 20,
            color: promotion.isActive ? Colors.orange : Colors.green,
          ),
        if (onDelete != null)
          IconButton(
            onPressed: onDelete,
            icon: const Icon(Icons.delete_outline),
            tooltip: 'Delete Promotion',
            iconSize: 20,
            color: Colors.red,
          ),
      ],
    );
  }

  Color _getStatusColor() {
    if (promotion.isExpired) return Colors.red;
    if (promotion.isNotStarted) return Colors.orange;
    if (promotion.isCurrentlyActive) return Colors.green;
    return Colors.grey;
  }

  String _getStatusText() {
    if (promotion.isExpired) return 'Expired';
    if (promotion.isNotStarted) return 'Upcoming';
    if (promotion.isCurrentlyActive) return 'Active';
    if (promotion.isActive) return 'Scheduled';
    return 'Inactive';
  }
}
