import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_state.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_event.dart';
import 'package:supplier_app/presentation/widgets/menu/dish_card.dart';
import 'package:supplier_app/presentation/widgets/common/empty_state_widget.dart';
import 'package:supplier_app/presentation/widgets/common/loading_widget.dart';

class DishesTab extends StatelessWidget {
  const DishesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MenuBloc, MenuState>(
      listener: (context, state) {
        if (state is MenuDishCreateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Dish "${state.dish.name}" created successfully'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        } else if (state is MenuDishCreateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } else if (state is MenuDishUpdateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Dish "${state.dish.name}" updated successfully'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        } else if (state is MenuDishUpdateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } else if (state is MenuDishDeleteSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Dish deleted successfully'),
            ),
          );
        } else if (state is MenuDishDeleteError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is MenuLoading) {
          return const LoadingWidget(message: 'Loading dishes...');
        }

        if (state is MenuError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load dishes',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    context.read<MenuBloc>().add(const MenuDishesLoadRequested());
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is MenuLoaded) {
          return Column(
            children: [
              // Category Filter
              if (state.categories.isNotEmpty) _buildCategoryFilter(context, state),
              
              // Dishes List
              Expanded(
                child: _buildDishesList(context, state),
              ),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildCategoryFilter(BuildContext context, MenuLoaded state) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: state.categories.length + 1, // +1 for "All" option
        itemBuilder: (context, index) {
          if (index == 0) {
            // "All" filter
            final isSelected = state.selectedCategoryId == null;
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: const Text('All'),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    context.read<MenuBloc>().add(const MenuFilterChanged());
                  }
                },
              ),
            );
          }
          
          final category = state.categories[index - 1];
          final isSelected = state.selectedCategoryId == category.id;
          
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category.name),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  context.read<MenuBloc>().add(
                    MenuFilterChanged(categoryId: category.id),
                  );
                } else {
                  context.read<MenuBloc>().add(const MenuFilterChanged());
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildDishesList(BuildContext context, MenuLoaded state) {
    final dishes = state.filteredDishes;
    
    if (dishes.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.restaurant_menu_outlined,
        title: 'No Dishes',
        message: 'Start by adding dishes to your menu. Customers will see them in your restaurant.',
        actionText: 'Add Dish',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<MenuBloc>().add(const MenuDishesLoadRequested());
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: dishes.length,
        itemBuilder: (context, index) {
          final dish = dishes[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: DishCard(
              dish: dish,
              onEdit: () {
                // TODO: Navigate to edit dish screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Edit dish feature coming soon'),
                  ),
                );
              },
              onDelete: () {
                _showDeleteDialog(context, dish.id, dish.name);
              },
              onToggleAvailability: (isAvailable) {
                context.read<MenuBloc>().add(
                  MenuDishAvailabilityToggleRequested(dish.id, isAvailable),
                );
              },
            ),
          );
        },
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, String dishId, String dishName) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Dish'),
        content: Text(
          'Are you sure you want to delete "$dishName"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              context.read<MenuBloc>().add(
                MenuDishDeleteRequested(dishId),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
