import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_state.dart';
import 'package:supplier_app/presentation/widgets/common/loading_widget.dart';
import 'package:supplier_app/presentation/widgets/common/empty_state_widget.dart';

class PopularDishesTab extends StatelessWidget {
  const PopularDishesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoading || state is AnalyticsPopularDishesLoading) {
          return const LoadingWidget(message: 'Loading popular dishes...');
        }

        if (state is AnalyticsError || state is AnalyticsPopularDishesError) {
          final message = state is AnalyticsError 
              ? state.message 
              : (state as AnalyticsPopularDishesError).message;
          
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load popular dishes',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Retry loading popular dishes
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return const EmptyStateWidget(
          icon: Icons.restaurant_menu_outlined,
          title: 'Popular Dishes',
          message: 'Popular dishes analysis coming soon with rankings and performance metrics.',
        );
      },
    );
  }
}
