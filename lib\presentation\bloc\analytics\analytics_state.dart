import 'package:equatable/equatable.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/data/models/analytics.dart';

abstract class AnalyticsState extends Equatable {
  const AnalyticsState();

  @override
  List<Object?> get props => [];
}

class AnalyticsInitial extends AnalyticsState {
  const AnalyticsInitial();
}

class AnalyticsLoading extends AnalyticsState {
  const AnalyticsLoading();
}

class AnalyticsLoaded extends AnalyticsState {
  final SalesReport? salesReport;
  final List<OrderTrend>? orderTrends;
  final List<PopularDish>? popularDishes;
  final List<CustomerFeedback>? customerFeedback;
  final Map<String, dynamic>? revenueAnalytics;
  final Map<String, dynamic>? performanceMetrics;
  final DateTime? startDate;
  final DateTime? endDate;
  final ReportType? reportType;
  final bool isRefreshing;

  const AnalyticsLoaded({
    this.salesReport,
    this.orderTrends,
    this.popularDishes,
    this.customerFeedback,
    this.revenueAnalytics,
    this.performanceMetrics,
    this.startDate,
    this.endDate,
    this.reportType,
    this.isRefreshing = false,
  });

  @override
  List<Object?> get props => [
        salesReport,
        orderTrends,
        popularDishes,
        customerFeedback,
        revenueAnalytics,
        performanceMetrics,
        startDate,
        endDate,
        reportType,
        isRefreshing,
      ];

  AnalyticsLoaded copyWith({
    SalesReport? salesReport,
    List<OrderTrend>? orderTrends,
    List<PopularDish>? popularDishes,
    List<CustomerFeedback>? customerFeedback,
    Map<String, dynamic>? revenueAnalytics,
    Map<String, dynamic>? performanceMetrics,
    DateTime? startDate,
    DateTime? endDate,
    ReportType? reportType,
    bool? isRefreshing,
  }) {
    return AnalyticsLoaded(
      salesReport: salesReport ?? this.salesReport,
      orderTrends: orderTrends ?? this.orderTrends,
      popularDishes: popularDishes ?? this.popularDishes,
      customerFeedback: customerFeedback ?? this.customerFeedback,
      revenueAnalytics: revenueAnalytics ?? this.revenueAnalytics,
      performanceMetrics: performanceMetrics ?? this.performanceMetrics,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      reportType: reportType ?? this.reportType,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

class AnalyticsError extends AnalyticsState {
  final String message;

  const AnalyticsError(this.message);

  @override
  List<Object> get props => [message];
}

// Sales Report States
class AnalyticsSalesReportLoading extends AnalyticsState {
  final ReportType type;

  const AnalyticsSalesReportLoading(this.type);

  @override
  List<Object> get props => [type];
}

class AnalyticsSalesReportLoaded extends AnalyticsState {
  final SalesReport salesReport;

  const AnalyticsSalesReportLoaded(this.salesReport);

  @override
  List<Object> get props => [salesReport];
}

class AnalyticsSalesReportError extends AnalyticsState {
  final String message;

  const AnalyticsSalesReportError(this.message);

  @override
  List<Object> get props => [message];
}

// Order Trends States
class AnalyticsOrderTrendsLoading extends AnalyticsState {
  const AnalyticsOrderTrendsLoading();
}

class AnalyticsOrderTrendsLoaded extends AnalyticsState {
  final List<OrderTrend> orderTrends;

  const AnalyticsOrderTrendsLoaded(this.orderTrends);

  @override
  List<Object> get props => [orderTrends];
}

class AnalyticsOrderTrendsError extends AnalyticsState {
  final String message;

  const AnalyticsOrderTrendsError(this.message);

  @override
  List<Object> get props => [message];
}

// Popular Dishes States
class AnalyticsPopularDishesLoading extends AnalyticsState {
  const AnalyticsPopularDishesLoading();
}

class AnalyticsPopularDishesLoaded extends AnalyticsState {
  final List<PopularDish> popularDishes;

  const AnalyticsPopularDishesLoaded(this.popularDishes);

  @override
  List<Object> get props => [popularDishes];
}

class AnalyticsPopularDishesError extends AnalyticsState {
  final String message;

  const AnalyticsPopularDishesError(this.message);

  @override
  List<Object> get props => [message];
}

// Customer Feedback States
class AnalyticsCustomerFeedbackLoading extends AnalyticsState {
  const AnalyticsCustomerFeedbackLoading();
}

class AnalyticsCustomerFeedbackLoaded extends AnalyticsState {
  final List<CustomerFeedback> customerFeedback;

  const AnalyticsCustomerFeedbackLoaded(this.customerFeedback);

  @override
  List<Object> get props => [customerFeedback];
}

class AnalyticsCustomerFeedbackError extends AnalyticsState {
  final String message;

  const AnalyticsCustomerFeedbackError(this.message);

  @override
  List<Object> get props => [message];
}

// Revenue Analytics States
class AnalyticsRevenueLoading extends AnalyticsState {
  const AnalyticsRevenueLoading();
}

class AnalyticsRevenueLoaded extends AnalyticsState {
  final Map<String, dynamic> revenueAnalytics;

  const AnalyticsRevenueLoaded(this.revenueAnalytics);

  @override
  List<Object> get props => [revenueAnalytics];
}

class AnalyticsRevenueError extends AnalyticsState {
  final String message;

  const AnalyticsRevenueError(this.message);

  @override
  List<Object> get props => [message];
}

// Performance Metrics States
class AnalyticsPerformanceMetricsLoading extends AnalyticsState {
  const AnalyticsPerformanceMetricsLoading();
}

class AnalyticsPerformanceMetricsLoaded extends AnalyticsState {
  final Map<String, dynamic> performanceMetrics;

  const AnalyticsPerformanceMetricsLoaded(this.performanceMetrics);

  @override
  List<Object> get props => [performanceMetrics];
}

class AnalyticsPerformanceMetricsError extends AnalyticsState {
  final String message;

  const AnalyticsPerformanceMetricsError(this.message);

  @override
  List<Object> get props => [message];
}
