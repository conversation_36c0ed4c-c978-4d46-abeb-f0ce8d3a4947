import 'package:flutter/material.dart';

class QuickActionsCard extends StatelessWidget {
  final VoidCallback? onAddMenuItem;
  final VoidCallback? onCreatePromotion;
  final VoidCallback? onViewAnalytics;
  final VoidCallback? onManageOrders;

  const QuickActionsCard({
    super.key,
    this.onAddMenuItem,
    this.onCreatePromotion,
    this.onViewAnalytics,
    this.onManageOrders,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 2.8, // Increased to prevent overflow
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildActionButton(
                  context,
                  'Add Menu Item',
                  Icons.restaurant_menu,
                  Colors.blue,
                  onAddMenuItem,
                ),
                _buildActionButton(
                  context,
                  'Create Promotion',
                  Icons.local_offer,
                  Colors.orange,
                  onCreatePromotion,
                ),
                _buildActionButton(
                  context,
                  'View Analytics',
                  Icons.analytics,
                  Colors.green,
                  onViewAnalytics,
                ),
                _buildActionButton(
                  context,
                  'Manage Orders',
                  Icons.receipt_long,
                  Colors.purple,
                  onManageOrders,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
