import 'package:cloud_firestore/cloud_firestore.dart' hide Order;
import 'package:flutter/foundation.dart';
import 'package:supplier_app/data/models/analytics.dart';
import 'package:supplier_app/data/models/order.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

class FirebaseAnalyticsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String _ordersCollection = 'orders';
  static const String _dishesCollection = 'dishes';
  static const String _feedbackCollection = 'customer_feedback';

  // Get sales report
  Future<SalesReport> getSalesReport(
    String restaurantId, {
    required ReportType type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final dates = _getDateRange(type, startDate, endDate);
      final start = dates['start']!;
      final end = dates['end']!;

      final snapshot = await _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('status', isEqualTo: OrderStatus.delivered.name)
          .where('orderTime', isGreaterThanOrEqualTo: Timestamp.fromDate(start))
          .where('orderTime', isLessThanOrEqualTo: Timestamp.fromDate(end))
          .get();

      double totalRevenue = 0.0;
      double totalTax = 0.0;
      double totalDeliveryFees = 0.0;
      double totalDiscounts = 0.0;
      int totalOrders = snapshot.docs.length;
      int completedOrders = snapshot.docs.length;
      int cancelledOrders = 0;

      final dailySalesMap = <String, DailySales>{};
      final dishSalesMap = <String, PopularDish>{};
      final categorySalesMap = <String, CategorySales>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final orderTime = (data['orderTime'] as Timestamp).toDate();
        final total = (data['total'] as num?)?.toDouble() ?? 0.0;
        final tax = (data['tax'] as num?)?.toDouble() ?? 0.0;
        final deliveryFee = (data['deliveryFee'] as num?)?.toDouble() ?? 0.0;
        final discount = (data['discount'] as num?)?.toDouble() ?? 0.0;

        totalRevenue += total;
        totalTax += tax;
        totalDeliveryFees += deliveryFee;
        totalDiscounts += discount;

        // Daily sales
        final dateKey =
            '${orderTime.year}-${orderTime.month.toString().padLeft(2, '0')}-${orderTime.day.toString().padLeft(2, '0')}';
        if (dailySalesMap.containsKey(dateKey)) {
          final existing = dailySalesMap[dateKey]!;
          dailySalesMap[dateKey] = DailySales(
            date: existing.date,
            revenue: existing.revenue + total,
            orders: existing.orders + 1,
            averageOrderValue:
                (existing.revenue + total) / (existing.orders + 1),
          );
        } else {
          dailySalesMap[dateKey] = DailySales(
            date: orderTime,
            revenue: total,
            orders: 1,
            averageOrderValue: total,
          );
        }

        // Process order items for dish and category sales
        final items = data['items'] as List? ?? [];
        for (final item in items) {
          final itemData = item as Map<String, dynamic>;
          final dishId = itemData['dishId'] as String;
          final dishName = itemData['dishName'] as String;
          final quantity = itemData['quantity'] as int;
          final price = (itemData['price'] as num).toDouble();
          final itemTotal = price * quantity;

          // Popular dishes
          if (dishSalesMap.containsKey(dishId)) {
            final existing = dishSalesMap[dishId]!;
            dishSalesMap[dishId] = PopularDish(
              dishId: existing.dishId,
              dishName: existing.dishName,
              orderCount: existing.orderCount + quantity,
              revenue: existing.revenue + itemTotal,
              averageRating: existing
                  .averageRating, // Would need to calculate from feedback
            );
          } else {
            dishSalesMap[dishId] = PopularDish(
              dishId: dishId,
              dishName: dishName,
              orderCount: quantity,
              revenue: itemTotal,
              averageRating: 0.0,
            );
          }
        }
      }

      // Get cancelled orders count
      final cancelledSnapshot = await _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('status',
              whereIn: [OrderStatus.cancelled.name, OrderStatus.rejected.name])
          .where('orderTime', isGreaterThanOrEqualTo: Timestamp.fromDate(start))
          .where('orderTime', isLessThanOrEqualTo: Timestamp.fromDate(end))
          .get();

      cancelledOrders = cancelledSnapshot.docs.length;

      return SalesReport(
        restaurantId: restaurantId,
        type: type,
        startDate: start,
        endDate: end,
        totalRevenue: totalRevenue,
        totalTax: totalTax,
        totalDeliveryFees: totalDeliveryFees,
        totalDiscounts: totalDiscounts,
        totalOrders: totalOrders + cancelledOrders,
        completedOrders: completedOrders,
        cancelledOrders: cancelledOrders,
        averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0.0,
        dailySales: dailySalesMap.values.toList()
          ..sort((a, b) => a.date.compareTo(b.date)),
        popularDishes: dishSalesMap.values.toList()
          ..sort((a, b) => b.revenue.compareTo(a.revenue)),
        categorySales: categorySalesMap.values.toList(),
      );
    } catch (e) {
      debugPrint('Error fetching sales report: $e');
      throw Exception('Failed to fetch sales report: $e');
    }
  }

  // Get order trends
  Future<List<OrderTrend>> getOrderTrends(
    String restaurantId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();

      final snapshot = await _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('orderTime', isGreaterThanOrEqualTo: Timestamp.fromDate(start))
          .where('orderTime', isLessThanOrEqualTo: Timestamp.fromDate(end))
          .get();

      final trendsMap = <String, OrderTrend>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final orderTime = (data['orderTime'] as Timestamp).toDate();
        final status = OrderStatus.values.firstWhere(
          (e) => e.name == data['status'],
          orElse: () => OrderStatus.pending,
        );

        final dateKey =
            '${orderTime.year}-${orderTime.month.toString().padLeft(2, '0')}-${orderTime.day.toString().padLeft(2, '0')}';

        if (trendsMap.containsKey(dateKey)) {
          final existing = trendsMap[dateKey]!;
          trendsMap[dateKey] = OrderTrend(
            date: existing.date,
            totalOrders: existing.totalOrders + 1,
            completedOrders: existing.completedOrders +
                (status == OrderStatus.delivered ? 1 : 0),
            cancelledOrders: existing.cancelledOrders +
                ([OrderStatus.cancelled, OrderStatus.rejected].contains(status)
                    ? 1
                    : 0),
            averagePreparationTime:
                existing.averagePreparationTime, // Would need to calculate
          );
        } else {
          trendsMap[dateKey] = OrderTrend(
            date: orderTime,
            totalOrders: 1,
            completedOrders: status == OrderStatus.delivered ? 1 : 0,
            cancelledOrders:
                [OrderStatus.cancelled, OrderStatus.rejected].contains(status)
                    ? 1
                    : 0,
            averagePreparationTime: 0.0,
          );
        }
      }

      return trendsMap.values.toList()
        ..sort((a, b) => a.date.compareTo(b.date));
    } catch (e) {
      debugPrint('Error fetching order trends: $e');
      throw Exception('Failed to fetch order trends: $e');
    }
  }

  // Get popular dishes
  Future<List<PopularDish>> getPopularDishes(
    String restaurantId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();

      final snapshot = await _firestore
          .collection(_ordersCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('status', isEqualTo: OrderStatus.delivered.name)
          .where('orderTime', isGreaterThanOrEqualTo: Timestamp.fromDate(start))
          .where('orderTime', isLessThanOrEqualTo: Timestamp.fromDate(end))
          .get();

      final dishSalesMap = <String, PopularDish>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final items = data['items'] as List? ?? [];

        for (final item in items) {
          final itemData = item as Map<String, dynamic>;
          final dishId = itemData['dishId'] as String;
          final dishName = itemData['dishName'] as String;
          final quantity = itemData['quantity'] as int;
          final price = (itemData['price'] as num).toDouble();
          final itemTotal = price * quantity;

          if (dishSalesMap.containsKey(dishId)) {
            final existing = dishSalesMap[dishId]!;
            dishSalesMap[dishId] = PopularDish(
              dishId: existing.dishId,
              dishName: existing.dishName,
              orderCount: existing.orderCount + quantity,
              revenue: existing.revenue + itemTotal,
              averageRating: existing.averageRating,
            );
          } else {
            dishSalesMap[dishId] = PopularDish(
              dishId: dishId,
              dishName: dishName,
              orderCount: quantity,
              revenue: itemTotal,
              averageRating: 0.0,
            );
          }
        }
      }

      final popularDishes = dishSalesMap.values.toList()
        ..sort((a, b) => b.orderCount.compareTo(a.orderCount));

      return popularDishes.take(limit).toList();
    } catch (e) {
      debugPrint('Error fetching popular dishes: $e');
      throw Exception('Failed to fetch popular dishes: $e');
    }
  }

  // Get customer feedback
  Future<List<CustomerFeedback>> getCustomerFeedback(
    String restaurantId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore
          .collection(_feedbackCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (startDate != null) {
        query = query.where('createdAt',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('createdAt',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return CustomerFeedback.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching customer feedback: $e');
      throw Exception('Failed to fetch customer feedback: $e');
    }
  }

  // Get revenue analytics
  Future<Map<String, dynamic>> getRevenueAnalytics(
    String restaurantId, {
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final salesReport = await getSalesReport(
        restaurantId,
        type: ReportType.custom,
        startDate: startDate,
        endDate: endDate,
      );

      return {
        'totalRevenue': salesReport.totalRevenue,
        'totalOrders': salesReport.totalOrders,
        'averageOrderValue': salesReport.averageOrderValue,
        'completionRate': salesReport.totalOrders > 0
            ? (salesReport.completedOrders / salesReport.totalOrders) * 100
            : 0.0,
        'dailySales': salesReport.dailySales.map((ds) => ds.toJson()).toList(),
      };
    } catch (e) {
      debugPrint('Error fetching revenue analytics: $e');
      throw Exception('Failed to fetch revenue analytics: $e');
    }
  }

  // Get performance metrics
  Future<Map<String, dynamic>> getPerformanceMetrics(
    String restaurantId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();

      final orderTrends =
          await getOrderTrends(restaurantId, startDate: start, endDate: end);
      final popularDishes =
          await getPopularDishes(restaurantId, startDate: start, endDate: end);

      final totalOrders =
          orderTrends.fold<int>(0, (sum, trend) => sum + trend.totalOrders);
      final completedOrders =
          orderTrends.fold<int>(0, (sum, trend) => sum + trend.completedOrders);
      final cancelledOrders =
          orderTrends.fold<int>(0, (sum, trend) => sum + trend.cancelledOrders);

      return {
        'totalOrders': totalOrders,
        'completedOrders': completedOrders,
        'cancelledOrders': cancelledOrders,
        'completionRate':
            totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0.0,
        'cancellationRate':
            totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0.0,
        'topDishes':
            popularDishes.take(5).map((dish) => dish.toJson()).toList(),
        'orderTrends': orderTrends.map((trend) => trend.toJson()).toList(),
      };
    } catch (e) {
      debugPrint('Error fetching performance metrics: $e');
      throw Exception('Failed to fetch performance metrics: $e');
    }
  }

  // Helper method to get date range based on report type
  Map<String, DateTime> _getDateRange(
      ReportType type, DateTime? startDate, DateTime? endDate) {
    final now = DateTime.now();

    switch (type) {
      case ReportType.daily:
        final start = DateTime(now.year, now.month, now.day);
        return {'start': start, 'end': start.add(const Duration(days: 1))};
      case ReportType.weekly:
        final start = now.subtract(Duration(days: now.weekday - 1));
        return {
          'start': DateTime(start.year, start.month, start.day),
          'end': now
        };
      case ReportType.monthly:
        final start = DateTime(now.year, now.month, 1);
        return {'start': start, 'end': now};
      case ReportType.yearly:
        final start = DateTime(now.year, 1, 1);
        return {'start': start, 'end': now};
      case ReportType.custom:
        return {
          'start': startDate ?? now.subtract(const Duration(days: 30)),
          'end': endDate ?? now,
        };
    }
  }
}
