import 'package:equatable/equatable.dart';
import 'package:supplier_app/data/models/promotion.dart';

abstract class PromotionsState extends Equatable {
  const PromotionsState();

  @override
  List<Object?> get props => [];
}

class PromotionsInitial extends PromotionsState {
  const PromotionsInitial();
}

class PromotionsLoading extends PromotionsState {
  const PromotionsLoading();
}

class PromotionsLoaded extends PromotionsState {
  final List<Promotion> promotions;
  final bool? currentFilter;
  final bool isRefreshing;

  const PromotionsLoaded({
    required this.promotions,
    this.currentFilter,
    this.isRefreshing = false,
  });

  @override
  List<Object?> get props => [promotions, currentFilter, isRefreshing];

  PromotionsLoaded copyWith({
    List<Promotion>? promotions,
    bool? currentFilter,
    bool? isRefreshing,
  }) {
    return PromotionsLoaded(
      promotions: promotions ?? this.promotions,
      currentFilter: currentFilter ?? this.currentFilter,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  // Convenience getters
  List<Promotion> get activePromotions => promotions.where((p) => p.isCurrentlyActive).toList();
  List<Promotion> get inactivePromotions => promotions.where((p) => !p.isCurrentlyActive).toList();
  List<Promotion> get expiredPromotions => promotions.where((p) => p.isExpired).toList();
  List<Promotion> get upcomingPromotions => promotions.where((p) => p.isNotStarted).toList();
}

class PromotionsError extends PromotionsState {
  final String message;

  const PromotionsError(this.message);

  @override
  List<Object> get props => [message];
}

// Promotion Details States
class PromotionDetailsLoading extends PromotionsState {
  final String promotionId;

  const PromotionDetailsLoading(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionDetailsLoaded extends PromotionsState {
  final Promotion promotion;

  const PromotionDetailsLoaded(this.promotion);

  @override
  List<Object> get props => [promotion];
}

class PromotionDetailsError extends PromotionsState {
  final String message;
  final String promotionId;

  const PromotionDetailsError(this.message, this.promotionId);

  @override
  List<Object> get props => [message, promotionId];
}

// Promotion CRUD States
class PromotionCreating extends PromotionsState {
  const PromotionCreating();
}

class PromotionCreateSuccess extends PromotionsState {
  final Promotion promotion;

  const PromotionCreateSuccess(this.promotion);

  @override
  List<Object> get props => [promotion];
}

class PromotionCreateError extends PromotionsState {
  final String message;

  const PromotionCreateError(this.message);

  @override
  List<Object> get props => [message];
}

class PromotionUpdating extends PromotionsState {
  final String promotionId;

  const PromotionUpdating(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionUpdateSuccess extends PromotionsState {
  final Promotion promotion;

  const PromotionUpdateSuccess(this.promotion);

  @override
  List<Object> get props => [promotion];
}

class PromotionUpdateError extends PromotionsState {
  final String message;

  const PromotionUpdateError(this.message);

  @override
  List<Object> get props => [message];
}

class PromotionDeleting extends PromotionsState {
  final String promotionId;

  const PromotionDeleting(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionDeleteSuccess extends PromotionsState {
  final String promotionId;

  const PromotionDeleteSuccess(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionDeleteError extends PromotionsState {
  final String message;

  const PromotionDeleteError(this.message);

  @override
  List<Object> get props => [message];
}

// Promotion Usage States
class PromotionUsageLoading extends PromotionsState {
  final String promotionId;

  const PromotionUsageLoading(this.promotionId);

  @override
  List<Object> get props => [promotionId];
}

class PromotionUsageLoaded extends PromotionsState {
  final String promotionId;
  final List<PromotionUsage> usage;

  const PromotionUsageLoaded(this.promotionId, this.usage);

  @override
  List<Object> get props => [promotionId, usage];
}

class PromotionUsageError extends PromotionsState {
  final String message;
  final String promotionId;

  const PromotionUsageError(this.message, this.promotionId);

  @override
  List<Object> get props => [message, promotionId];
}

// Active Promotions States
class ActivePromotionsLoading extends PromotionsState {
  const ActivePromotionsLoading();
}

class ActivePromotionsLoaded extends PromotionsState {
  final List<Promotion> activePromotions;

  const ActivePromotionsLoaded(this.activePromotions);

  @override
  List<Object> get props => [activePromotions];
}

class ActivePromotionsError extends PromotionsState {
  final String message;

  const ActivePromotionsError(this.message);

  @override
  List<Object> get props => [message];
}
