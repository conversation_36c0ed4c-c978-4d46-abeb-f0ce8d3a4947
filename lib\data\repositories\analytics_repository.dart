import 'package:supplier_app/core/services/firebase_analytics_service.dart';
import 'package:supplier_app/data/models/analytics.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

class AnalyticsRepository {
  final FirebaseAnalyticsService _firebaseAnalyticsService;
  final AuthRepository _authRepository;

  AnalyticsRepository({
    FirebaseAnalyticsService? firebaseAnalyticsService,
    AuthRepository? authRepository,
  })  : _firebaseAnalyticsService =
            firebaseAnalyticsService ?? FirebaseAnalyticsService(),
        _authRepository = authRepository ?? AuthRepository();

  // Get current restaurant ID from auth
  Future<String> _getRestaurantId() async {
    final user = await _authRepository.getCurrentUser();
    if (user == null) throw AnalyticsException('Not authenticated');
    return user.id; // Assuming user.id is the restaurant ID
  }

  // Get sales report
  Future<SalesReport> getSalesReport({
    required ReportType type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseAnalyticsService.getSalesReport(
        restaurantId,
        type: type,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw AnalyticsException('Failed to fetch sales report: ${e.toString()}');
    }
  }

  // Get order trends
  Future<List<OrderTrend>> getOrderTrends({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseAnalyticsService.getOrderTrends(
        restaurantId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw AnalyticsException('Failed to fetch order trends: ${e.toString()}');
    }
  }

  // Get popular dishes
  Future<List<PopularDish>> getPopularDishes({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseAnalyticsService.getPopularDishes(
        restaurantId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      throw AnalyticsException(
          'Failed to fetch popular dishes: ${e.toString()}');
    }
  }

  // Get customer feedback
  Future<List<CustomerFeedback>> getCustomerFeedback({
    DateTime? startDate,
    DateTime? endDate,
    double? minRating,
    double? maxRating,
    int page = 1,
    int limit = 50,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseAnalyticsService.getCustomerFeedback(
        restaurantId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      throw AnalyticsException(
          'Failed to fetch customer feedback: ${e.toString()}');
    }
  }

  // Get revenue analytics
  Future<Map<String, dynamic>> getRevenueAnalytics({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseAnalyticsService.getRevenueAnalytics(
        restaurantId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw AnalyticsException(
          'Failed to fetch revenue analytics: ${e.toString()}');
    }
  }

  // Get performance metrics
  Future<Map<String, dynamic>> getPerformanceMetrics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseAnalyticsService.getPerformanceMetrics(
        restaurantId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw AnalyticsException(
          'Failed to fetch performance metrics: ${e.toString()}');
    }
  }

  // Get dashboard analytics (summary for dashboard)
  Future<Map<String, dynamic>> getDashboardAnalytics() async {
    try {
      final restaurantId = await _getRestaurantId();
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Get current month performance
      final performanceMetrics =
          await _firebaseAnalyticsService.getPerformanceMetrics(
        restaurantId,
        startDate: startOfMonth,
        endDate: now,
      );

      // Get revenue analytics for current month
      final revenueAnalytics =
          await _firebaseAnalyticsService.getRevenueAnalytics(
        restaurantId,
        startDate: startOfMonth,
        endDate: now,
      );

      // Get popular dishes for current month
      final popularDishes = await _firebaseAnalyticsService.getPopularDishes(
        restaurantId,
        startDate: startOfMonth,
        endDate: now,
        limit: 5,
      );

      return {
        'performance': performanceMetrics,
        'revenue': revenueAnalytics,
        'popularDishes': popularDishes.map((dish) => dish.toJson()).toList(),
        'period': {
          'startDate': startOfMonth.toIso8601String(),
          'endDate': now.toIso8601String(),
          'type': 'monthly',
        },
      };
    } catch (e) {
      throw AnalyticsException(
          'Failed to fetch dashboard analytics: ${e.toString()}');
    }
  }

  // Get weekly performance for dashboard chart
  Future<List<double>> getWeeklyPerformanceData() async {
    try {
      final restaurantId = await _getRestaurantId();
      final now = DateTime.now();
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

      final orderTrends = await _firebaseAnalyticsService.getOrderTrends(
        restaurantId,
        startDate: startOfWeek,
        endDate: now,
      );

      // Convert to weekly data array (7 days)
      final weeklyData = List<double>.filled(7, 0.0);

      for (final trend in orderTrends) {
        final dayIndex = trend.date.weekday - 1; // Monday = 0, Sunday = 6
        if (dayIndex >= 0 && dayIndex < 7) {
          weeklyData[dayIndex] = trend.completedOrders.toDouble();
        }
      }

      return weeklyData;
    } catch (e) {
      throw AnalyticsException(
          'Failed to fetch weekly performance data: ${e.toString()}');
    }
  }
}

class AnalyticsException implements Exception {
  final String message;

  const AnalyticsException(this.message);

  @override
  String toString() => 'AnalyticsException: $message';
}
