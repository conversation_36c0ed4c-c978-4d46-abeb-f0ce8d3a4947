import 'dart:io';
import 'package:supplier_app/core/services/firebase_menu_service.dart';
import 'package:supplier_app/data/models/menu.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';

class MenuRepository {
  final FirebaseMenuService _firebaseMenuService;
  final AuthRepository _authRepository;

  MenuRepository({
    FirebaseMenuService? firebaseMenuService,
    AuthRepository? authRepository,
  })  : _firebaseMenuService = firebaseMenuService ?? FirebaseMenuService(),
        _authRepository = authRepository ?? AuthRepository();

  // Get current restaurant ID from auth
  Future<String> _getRestaurantId() async {
    final user = await _authRepository.getCurrentUser();
    if (user == null) throw MenuException('Not authenticated');
    return user.id; // Assuming user.id is the restaurant ID
  }

  // CATEGORIES OPERATIONS

  // Get categories
  Future<List<MenuCategory>> getCategories() async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseMenuService.getCategories(restaurantId);
    } catch (e) {
      throw MenuException('Failed to fetch categories: ${e.toString()}');
    }
  }

  // Get categories stream for real-time updates
  Stream<List<MenuCategory>> getCategoriesStream() async* {
    try {
      final restaurantId = await _getRestaurantId();
      yield* _firebaseMenuService.getCategoriesStream(restaurantId);
    } catch (e) {
      throw MenuException('Failed to get categories stream: ${e.toString()}');
    }
  }

  // Create category
  Future<MenuCategory> createCategory(MenuCategory category) async {
    try {
      final restaurantId = await _getRestaurantId();
      final sortOrder =
          await _firebaseMenuService.getNextCategorySortOrder(restaurantId);

      final categoryWithSortOrder = category.copyWith(
        sortOrder: sortOrder,
      );

      return await _firebaseMenuService.createCategory(categoryWithSortOrder);
    } catch (e) {
      throw MenuException('Failed to create category: ${e.toString()}');
    }
  }

  // Update category
  Future<MenuCategory> updateCategory(MenuCategory category) async {
    try {
      return await _firebaseMenuService.updateCategory(category);
    } catch (e) {
      throw MenuException('Failed to update category: ${e.toString()}');
    }
  }

  // Delete category
  Future<void> deleteCategory(String categoryId) async {
    try {
      await _firebaseMenuService.deleteCategory(categoryId);
    } catch (e) {
      throw MenuException('Failed to delete category: ${e.toString()}');
    }
  }

  // DISHES OPERATIONS

  // Get dishes
  Future<List<Dish>> getDishes({String? categoryId}) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseMenuService.getDishes(restaurantId,
          categoryId: categoryId);
    } catch (e) {
      throw MenuException('Failed to fetch dishes: ${e.toString()}');
    }
  }

  // Get dishes stream for real-time updates
  Stream<List<Dish>> getDishesStream({String? categoryId}) async* {
    try {
      final restaurantId = await _getRestaurantId();
      yield* _firebaseMenuService.getDishesStream(restaurantId,
          categoryId: categoryId);
    } catch (e) {
      throw MenuException('Failed to get dishes stream: ${e.toString()}');
    }
  }

  // Get single dish
  Future<Dish?> getDish(String dishId) async {
    try {
      return await _firebaseMenuService.getDish(dishId);
    } catch (e) {
      throw MenuException('Failed to fetch dish: ${e.toString()}');
    }
  }

  // Create dish
  Future<Dish> createDish(Dish dish) async {
    try {
      final restaurantId = await _getRestaurantId();
      final sortOrder = await _firebaseMenuService.getNextDishSortOrder(
        restaurantId,
        dish.categoryId,
      );

      final dishWithSortOrder = dish.copyWith(
        sortOrder: sortOrder,
      );

      return await _firebaseMenuService.createDish(dishWithSortOrder);
    } catch (e) {
      throw MenuException('Failed to create dish: ${e.toString()}');
    }
  }

  // Update dish
  Future<Dish> updateDish(Dish dish) async {
    try {
      return await _firebaseMenuService.updateDish(dish);
    } catch (e) {
      throw MenuException('Failed to update dish: ${e.toString()}');
    }
  }

  // Delete dish
  Future<void> deleteDish(String dishId) async {
    try {
      await _firebaseMenuService.deleteDish(dishId);
    } catch (e) {
      throw MenuException('Failed to delete dish: ${e.toString()}');
    }
  }

  // Toggle dish availability
  Future<Dish> toggleDishAvailability(String dishId, bool isAvailable) async {
    try {
      await _firebaseMenuService.toggleDishAvailability(dishId, isAvailable);
      // Fetch and return the updated dish
      final updatedDish = await getDish(dishId);
      if (updatedDish == null) {
        throw MenuException('Dish not found after availability toggle');
      }
      return updatedDish;
    } catch (e) {
      throw MenuException(
          'Failed to toggle dish availability: ${e.toString()}');
    }
  }

  // IMAGE OPERATIONS

  // Upload image
  Future<String> uploadImage(File imageFile,
      {String? dishId, String? categoryId}) async {
    try {
      final restaurantId = await _getRestaurantId();
      return await _firebaseMenuService.uploadImage(
        imageFile,
        restaurantId,
        dishId: dishId,
        categoryId: categoryId,
      );
    } catch (e) {
      throw MenuException('Failed to upload image: ${e.toString()}');
    }
  }

  // Delete image
  Future<void> deleteImage(String imageUrl) async {
    try {
      await _firebaseMenuService.deleteImage(imageUrl);
    } catch (e) {
      throw MenuException('Failed to delete image: ${e.toString()}');
    }
  }
}

class MenuException implements Exception {
  final String message;

  const MenuException(this.message);

  @override
  String toString() => 'MenuException: $message';
}
