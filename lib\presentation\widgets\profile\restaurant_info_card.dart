import 'package:flutter/material.dart';

class RestaurantInfoCard extends StatelessWidget {
  final String restaurantId;
  final VoidCallback? onEdit;

  const RestaurantInfoCard({
    super.key,
    required this.restaurantId,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Restaurant Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Restaurant Details
            _buildInfoRow(
              context,
              'Restaurant Name',
              'My Restaurant', // TODO: Get from restaurant data
              Icons.restaurant,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              context,
              'Cuisine Type',
              'Multi-Cuisine', // TODO: Get from restaurant data
              Icons.local_dining,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              context,
              'Address',
              '123 Main Street, City, State 12345', // TODO: Get from restaurant data
              Icons.location_on,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              context,
              'Phone',
              '+****************', // TODO: Get from restaurant data
              Icons.phone,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              context,
              'Rating',
              '4.5 ⭐ (125 reviews)', // TODO: Get from restaurant data
              Icons.star,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
