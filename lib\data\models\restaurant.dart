import 'package:equatable/equatable.dart';
import 'package:supplier_app/core/constants/app_constants.dart';

class Restaurant extends Equatable {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String description;
  final Address address;
  final List<String> cuisineTypes;
  final RestaurantStatus status;
  final BusinessHours businessHours;
  final BankDetails bankDetails;
  final double rating;
  final int totalReviews;
  final String? logoUrl;
  final String? coverImageUrl;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Restaurant({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.description,
    required this.address,
    required this.cuisineTypes,
    required this.status,
    required this.businessHours,
    required this.bankDetails,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.logoUrl,
    this.coverImageUrl,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        description,
        address,
        cuisineTypes,
        status,
        businessHours,
        bankDetails,
        rating,
        totalReviews,
        logoUrl,
        coverImageUrl,
        isVerified,
        createdAt,
        updatedAt,
      ];

  Restaurant copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? description,
    Address? address,
    List<String>? cuisineTypes,
    RestaurantStatus? status,
    BusinessHours? businessHours,
    BankDetails? bankDetails,
    double? rating,
    int? totalReviews,
    String? logoUrl,
    String? coverImageUrl,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Restaurant(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      description: description ?? this.description,
      address: address ?? this.address,
      cuisineTypes: cuisineTypes ?? this.cuisineTypes,
      status: status ?? this.status,
      businessHours: businessHours ?? this.businessHours,
      bankDetails: bankDetails ?? this.bankDetails,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      logoUrl: logoUrl ?? this.logoUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'description': description,
      'address': address.toJson(),
      'cuisineTypes': cuisineTypes,
      'status': status.name,
      'businessHours': businessHours.toJson(),
      'bankDetails': bankDetails.toJson(),
      'rating': rating,
      'totalReviews': totalReviews,
      'logoUrl': logoUrl,
      'coverImageUrl': coverImageUrl,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Restaurant.fromJson(Map<String, dynamic> json) {
    return Restaurant(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      description: json['description'] as String,
      address: Address.fromJson(json['address'] as Map<String, dynamic>),
      cuisineTypes: List<String>.from(json['cuisineTypes'] as List),
      status: RestaurantStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => RestaurantStatus.offline,
      ),
      businessHours: BusinessHours.fromJson(json['businessHours'] as Map<String, dynamic>),
      bankDetails: BankDetails.fromJson(json['bankDetails'] as Map<String, dynamic>),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalReviews: json['totalReviews'] as int? ?? 0,
      logoUrl: json['logoUrl'] as String?,
      coverImageUrl: json['coverImageUrl'] as String?,
      isVerified: json['isVerified'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
}

class Address extends Equatable {
  final String street;
  final String city;
  final String state;
  final String zipCode;
  final String country;
  final double? latitude;
  final double? longitude;

  const Address({
    required this.street,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    this.latitude,
    this.longitude,
  });

  @override
  List<Object?> get props => [street, city, state, zipCode, country, latitude, longitude];

  String get fullAddress => '$street, $city, $state $zipCode, $country';

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zipCode'] as String,
      country: json['country'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );
  }
}

class BusinessHours extends Equatable {
  final Map<String, DayHours> hours;

  const BusinessHours({required this.hours});

  @override
  List<Object> get props => [hours];

  Map<String, dynamic> toJson() {
    return {
      'hours': hours.map((key, value) => MapEntry(key, value.toJson())),
    };
  }

  factory BusinessHours.fromJson(Map<String, dynamic> json) {
    final hoursMap = <String, DayHours>{};
    final hoursJson = json['hours'] as Map<String, dynamic>;
    
    for (final entry in hoursJson.entries) {
      hoursMap[entry.key] = DayHours.fromJson(entry.value as Map<String, dynamic>);
    }
    
    return BusinessHours(hours: hoursMap);
  }
}

class DayHours extends Equatable {
  final bool isOpen;
  final String? openTime;
  final String? closeTime;

  const DayHours({
    required this.isOpen,
    this.openTime,
    this.closeTime,
  });

  @override
  List<Object?> get props => [isOpen, openTime, closeTime];

  Map<String, dynamic> toJson() {
    return {
      'isOpen': isOpen,
      'openTime': openTime,
      'closeTime': closeTime,
    };
  }

  factory DayHours.fromJson(Map<String, dynamic> json) {
    return DayHours(
      isOpen: json['isOpen'] as bool,
      openTime: json['openTime'] as String?,
      closeTime: json['closeTime'] as String?,
    );
  }
}

class BankDetails extends Equatable {
  final String accountNumber;
  final String routingNumber;
  final String bankName;
  final String accountHolderName;

  const BankDetails({
    required this.accountNumber,
    required this.routingNumber,
    required this.bankName,
    required this.accountHolderName,
  });

  @override
  List<Object> get props => [accountNumber, routingNumber, bankName, accountHolderName];

  Map<String, dynamic> toJson() {
    return {
      'accountNumber': accountNumber,
      'routingNumber': routingNumber,
      'bankName': bankName,
      'accountHolderName': accountHolderName,
    };
  }

  factory BankDetails.fromJson(Map<String, dynamic> json) {
    return BankDetails(
      accountNumber: json['accountNumber'] as String,
      routingNumber: json['routingNumber'] as String,
      bankName: json['bankName'] as String,
      accountHolderName: json['accountHolderName'] as String,
    );
  }
}
