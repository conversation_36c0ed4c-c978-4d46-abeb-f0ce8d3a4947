import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_event.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_state.dart';
import 'package:supplier_app/presentation/widgets/menu/categories_tab.dart';
import 'package:supplier_app/presentation/widgets/menu/dishes_tab.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load initial data
    context.read<MenuBloc>().add(const MenuCategoriesLoadRequested());
    context.read<MenuBloc>().add(const MenuDishesLoadRequested());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<MenuBloc, MenuState>(
        listener: (context, state) {
          if (state is MenuError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Tab Bar
            Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Categories'),
                  Tab(text: 'Dishes'),
                ],
              ),
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  CategoriesTab(),
                  DishesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          final isCategories = _tabController.index == 0;
          if (isCategories) {
            _navigateToAddCategory(context);
          } else {
            _navigateToAddDish(context);
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _navigateToAddCategory(BuildContext context) {
    CategoriesTab.navigateToAddCategory(context);
  }

  void _navigateToAddDish(BuildContext context) {
    DishesTab.navigateToAddDish(context);
  }
}
