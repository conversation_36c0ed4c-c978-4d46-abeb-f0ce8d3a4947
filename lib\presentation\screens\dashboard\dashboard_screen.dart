import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_bloc.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_event.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:supplier_app/presentation/widgets/dashboard/summary_card.dart';
import 'package:supplier_app/presentation/widgets/dashboard/status_toggle.dart';
import 'package:supplier_app/presentation/widgets/dashboard/recent_orders_list.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load dashboard data when screen initializes
    context.read<DashboardBloc>().add(const DashboardDataRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<DashboardBloc, DashboardState>(
        listener: (context, state) {
          if (state is DashboardError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is RestaurantStatusUpdateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is RestaurantStatusUpdateSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Restaurant status updated to ${state.status.displayName}'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is DashboardLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is DashboardError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load dashboard',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<DashboardBloc>().add(const DashboardDataRequested());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is DashboardLoaded) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<DashboardBloc>().add(const DashboardRefreshRequested());
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Restaurant Status Toggle
                    _buildStatusSection(state),
                    const SizedBox(height: 24),
                    
                    // Summary Cards
                    _buildSummaryCards(state),
                    const SizedBox(height: 24),
                    
                    // Recent Orders Section
                    _buildRecentOrdersSection(),
                  ],
                ),
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildStatusSection(DashboardLoaded state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Restaurant Status',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            RestaurantStatusToggle(
              currentStatus: state.restaurantStatus,
              onStatusChanged: (status) {
                context.read<DashboardBloc>().add(
                  RestaurantStatusToggleRequested(status),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(DashboardLoaded state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            SummaryCard(
              title: 'New Orders',
              value: state.newOrders.toString(),
              icon: Icons.receipt_long,
              color: Colors.blue,
              onTap: () {
                // TODO: Navigate to orders with pending filter
              },
            ),
            SummaryCard(
              title: 'In Progress',
              value: state.ordersInProgress.toString(),
              icon: Icons.schedule,
              color: Colors.orange,
              onTap: () {
                // TODO: Navigate to orders with in-progress filter
              },
            ),
            SummaryCard(
              title: 'Completed',
              value: state.completedOrders.toString(),
              icon: Icons.check_circle,
              color: Colors.green,
              onTap: () {
                // TODO: Navigate to orders with completed filter
              },
            ),
            SummaryCard(
              title: 'Daily Sales',
              value: '\$${state.dailySales.toStringAsFixed(2)}',
              icon: Icons.attach_money,
              color: Colors.purple,
              onTap: () {
                // TODO: Navigate to analytics
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentOrdersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Orders',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to orders screen
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const RecentOrdersList(),
      ],
    );
  }
}
