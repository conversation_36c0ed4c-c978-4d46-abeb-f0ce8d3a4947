import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_bloc.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_event.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_state.dart';
import 'package:supplier_app/presentation/widgets/dashboard/summary_card.dart';
import 'package:supplier_app/presentation/widgets/dashboard/status_toggle.dart';
import 'package:supplier_app/presentation/widgets/dashboard/recent_orders_list.dart';
import 'package:supplier_app/presentation/widgets/dashboard/quick_actions_card.dart';
import 'package:supplier_app/presentation/widgets/dashboard/performance_chart_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load dashboard data when screen initializes
    context.read<DashboardBloc>().add(const DashboardDataRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<DashboardBloc, DashboardState>(
        listener: (context, state) {
          if (state is DashboardError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is RestaurantStatusUpdateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is RestaurantStatusUpdateSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Restaurant status updated to ${state.status.displayName}'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is DashboardLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is DashboardError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load dashboard',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context
                          .read<DashboardBloc>()
                          .add(const DashboardDataRequested());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is DashboardLoaded) {
            return RefreshIndicator(
              onRefresh: () async {
                context
                    .read<DashboardBloc>()
                    .add(const DashboardRefreshRequested());
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Restaurant Status Toggle
                    _buildStatusSection(state),
                    const SizedBox(height: 24),

                    // Summary Cards
                    _buildSummaryCards(state),
                    const SizedBox(height: 24),

                    // Quick Actions
                    _buildQuickActionsSection(),
                    const SizedBox(height: 24),

                    // Performance Chart
                    _buildPerformanceChart(state),
                    const SizedBox(height: 24),

                    // Recent Orders Section
                    _buildRecentOrdersSection(),
                  ],
                ),
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildStatusSection(DashboardLoaded state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Restaurant Status',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            RestaurantStatusToggle(
              currentStatus: state.restaurantStatus,
              onStatusChanged: (status) {
                context.read<DashboardBloc>().add(
                      RestaurantStatusToggleRequested(status),
                    );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(DashboardLoaded state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            SummaryCard(
              title: 'New Orders',
              value: state.newOrders.toString(),
              icon: Icons.receipt_long,
              color: Colors.blue,
              onTap: () => _navigateToOrders(context, 'new'),
            ),
            SummaryCard(
              title: 'In Progress',
              value: state.ordersInProgress.toString(),
              icon: Icons.schedule,
              color: Colors.orange,
              onTap: () => _navigateToOrders(context, 'preparing'),
            ),
            SummaryCard(
              title: 'Completed',
              value: state.completedOrders.toString(),
              icon: Icons.check_circle,
              color: Colors.green,
              onTap: () => _navigateToOrders(context, 'completed'),
            ),
            SummaryCard(
              title: 'Daily Sales',
              value: '\$${state.dailySales.toStringAsFixed(2)}',
              icon: Icons.attach_money,
              color: Colors.purple,
              onTap: () => _navigateToAnalytics(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentOrdersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Orders',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            TextButton(
              onPressed: () => _navigateToOrders(context, 'all'),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const RecentOrdersList(),
      ],
    );
  }

  Widget _buildQuickActionsSection() {
    return QuickActionsCard(
      onAddMenuItem: () => _navigateToAddMenuItem(context),
      onCreatePromotion: () => _navigateToCreatePromotion(context),
      onViewAnalytics: () => _navigateToAnalytics(context),
      onManageOrders: () => _navigateToOrders(context, 'all'),
    );
  }

  Widget _buildPerformanceChart(DashboardLoaded state) {
    // Sample weekly data - in real app, this would come from the state
    final weeklyData = [12.0, 19.0, 15.0, 25.0, 22.0, 18.0, 20.0];

    return PerformanceChartCard(
      weeklyData: weeklyData,
      title: 'Weekly Orders',
      subtitle: 'Orders completed this week',
    );
  }

  // Navigation methods
  void _navigateToOrders(BuildContext context, String filter) {
    // Navigate to orders screen with specific filter
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigating to $filter orders...'),
      ),
    );
  }

  void _navigateToAnalytics(BuildContext context) {
    // Navigate to analytics screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigating to analytics...'),
      ),
    );
  }

  void _navigateToAddMenuItem(BuildContext context) {
    // Navigate to add menu item screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigating to add menu item...'),
      ),
    );
  }

  void _navigateToCreatePromotion(BuildContext context) {
    // Navigate to create promotion screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigating to create promotion...'),
      ),
    );
  }
}
