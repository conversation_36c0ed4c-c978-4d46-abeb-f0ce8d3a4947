import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/main.dart';
import 'package:supplier_app/data/repositories/auth_repository.dart';
import 'package:supplier_app/data/repositories/order_repository.dart';
import 'package:supplier_app/data/repositories/menu_repository.dart';
import 'package:supplier_app/data/repositories/promotion_repository.dart';
import 'package:supplier_app/data/repositories/analytics_repository.dart';
import 'package:supplier_app/presentation/bloc/auth/auth_bloc.dart';
import 'package:supplier_app/presentation/bloc/dashboard/dashboard_bloc.dart';
import 'package:supplier_app/presentation/bloc/orders/orders_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_bloc.dart';
import 'package:supplier_app/presentation/bloc/promotions/promotions_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_bloc.dart';

void main() {
  group('Supplier App Comprehensive Tests', () {
    testWidgets('App initializes with login screen', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());
      await tester.pumpAndSettle();

      // Verify login screen is displayed
      expect(find.text('Supplier Portal'), findsOneWidget);
      expect(find.text('Login'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password fields
    });

    testWidgets('Login form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());
      await tester.pumpAndSettle();

      // Try to login without entering credentials
      final loginButton = find.text('Login');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Should show validation errors
      expect(find.text('Please enter your email'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('Email validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());
      await tester.pumpAndSettle();

      // Enter invalid email
      final emailField = find.byType(TextFormField).first;
      await tester.enterText(emailField, 'invalid-email');
      
      final loginButton = find.text('Login');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('Password visibility toggle works', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());
      await tester.pumpAndSettle();

      // Find password field and visibility toggle
      final passwordField = find.byType(TextFormField).last;
      final visibilityToggle = find.byIcon(Icons.visibility);

      // Initially password should be obscured
      final textField = tester.widget<TextFormField>(passwordField);
      expect(textField.obscureText, isTrue);

      // Tap visibility toggle
      await tester.tap(visibilityToggle);
      await tester.pumpAndSettle();

      // Now should show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('Remember me checkbox works', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());
      await tester.pumpAndSettle();

      // Find and tap remember me checkbox
      final checkbox = find.byType(Checkbox);
      expect(checkbox, findsOneWidget);

      // Initially should be unchecked
      Checkbox checkboxWidget = tester.widget<Checkbox>(checkbox);
      expect(checkboxWidget.value, isFalse);

      // Tap checkbox
      await tester.tap(checkbox);
      await tester.pumpAndSettle();

      // Should now be checked
      checkboxWidget = tester.widget<Checkbox>(checkbox);
      expect(checkboxWidget.value, isTrue);
    });

    testWidgets('Bloc providers are properly configured', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());

      // Verify all blocs are provided
      final context = tester.element(find.byType(MaterialApp));
      
      expect(() => context.read<AuthBloc>(), returnsNormally);
      expect(() => context.read<DashboardBloc>(), returnsNormally);
      expect(() => context.read<OrdersBloc>(), returnsNormally);
      expect(() => context.read<MenuBloc>(), returnsNormally);
      expect(() => context.read<PromotionsBloc>(), returnsNormally);
      expect(() => context.read<AnalyticsBloc>(), returnsNormally);
    });

    testWidgets('Theme is properly applied', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.theme, isNotNull);
      expect(materialApp.darkTheme, isNotNull);
    });

    testWidgets('Navigation structure is correct', (WidgetTester tester) async {
      await tester.pumpWidget(const SupplierApp());
      await tester.pumpAndSettle();

      // Should start with login screen
      expect(find.text('Supplier Portal'), findsOneWidget);
      
      // Should not show main navigation yet
      expect(find.byType(BottomNavigationBar), findsNothing);
    });
  });

  group('Widget Components Tests', () {
    testWidgets('Summary card displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(), // Placeholder for summary card test
          ),
        ),
      );

      // This would test individual widgets when they're isolated
      // For now, we're testing the overall app structure
    });
  });

  group('Repository Tests', () {
    test('AuthRepository can be instantiated', () {
      expect(() => AuthRepository(), returnsNormally);
    });

    test('OrderRepository can be instantiated', () {
      expect(() => OrderRepository(), returnsNormally);
    });

    test('MenuRepository can be instantiated', () {
      expect(() => MenuRepository(), returnsNormally);
    });

    test('PromotionRepository can be instantiated', () {
      expect(() => PromotionRepository(), returnsNormally);
    });

    test('AnalyticsRepository can be instantiated', () {
      expect(() => AnalyticsRepository(), returnsNormally);
    });
  });

  group('Bloc Tests', () {
    test('AuthBloc can be instantiated', () {
      final authRepository = AuthRepository();
      expect(() => AuthBloc(authRepository: authRepository), returnsNormally);
    });

    test('DashboardBloc can be instantiated', () {
      final orderRepository = OrderRepository();
      expect(() => DashboardBloc(orderRepository: orderRepository), returnsNormally);
    });

    test('OrdersBloc can be instantiated', () {
      final orderRepository = OrderRepository();
      expect(() => OrdersBloc(orderRepository: orderRepository), returnsNormally);
    });

    test('MenuBloc can be instantiated', () {
      final menuRepository = MenuRepository();
      expect(() => MenuBloc(menuRepository: menuRepository), returnsNormally);
    });

    test('PromotionsBloc can be instantiated', () {
      final promotionRepository = PromotionRepository();
      expect(() => PromotionsBloc(promotionRepository: promotionRepository), returnsNormally);
    });

    test('AnalyticsBloc can be instantiated', () {
      final analyticsRepository = AnalyticsRepository();
      expect(() => AnalyticsBloc(analyticsRepository: analyticsRepository), returnsNormally);
    });
  });
}
