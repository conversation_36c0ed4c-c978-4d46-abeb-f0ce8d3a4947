import 'package:flutter/material.dart';

class BusinessHoursCard extends StatelessWidget {
  final String restaurantId;
  final VoidCallback? onEdit;

  const BusinessHoursCard({
    super.key,
    required this.restaurantId,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    // Sample business hours - in real app, this would come from restaurant data
    final businessHours = {
      'Monday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
      'Tuesday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
      'Wednesday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
      'Thursday': {'open': '09:00', 'close': '22:00', 'isOpen': true},
      'Friday': {'open': '09:00', 'close': '23:00', 'isOpen': true},
      'Saturday': {'open': '09:00', 'close': '23:00', 'isOpen': true},
      'Sunday': {'open': '10:00', 'close': '21:00', 'isOpen': true},
    };

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Business Hours',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Business Hours List
            ...businessHours.entries.map((entry) {
              final day = entry.key;
              final hours = entry.value;
              final isOpen = hours['isOpen'] as bool;
              final openTime = hours['open'] as String;
              final closeTime = hours['close'] as String;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(
                        day,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: isOpen
                          ? Text(
                              '$openTime - $closeTime',
                              style: Theme.of(context).textTheme.bodyMedium,
                            )
                          : Text(
                              'Closed',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: isOpen ? Colors.green : Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            
            const SizedBox(height: 16),
            
            // Current Status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: Colors.green,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Currently Open', // TODO: Calculate based on current time
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Closes at 22:00', // TODO: Calculate next closing time
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
