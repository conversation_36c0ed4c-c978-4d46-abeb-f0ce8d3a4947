import 'package:flutter/material.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/data/models/order.dart';

class OrderFilterChips extends StatelessWidget {
  final OrderStatus? selectedStatus;
  final Function(OrderStatus?) onStatusChanged;
  final Map<OrderStatus, int> statusCounts;

  const OrderFilterChips({
    super.key,
    required this.selectedStatus,
    required this.onStatusChanged,
    this.statusCounts = const {},
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildFilterChip(
            context,
            'All',
            null,
            statusCounts.values.fold(0, (sum, count) => sum + count),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'New',
            OrderStatus.pending,
            statusCounts[OrderStatus.pending] ?? 0,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'Preparing',
            OrderStatus.preparing,
            statusCounts[OrderStatus.preparing] ?? 0,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'Ready',
            OrderStatus.ready,
            statusCounts[OrderStatus.ready] ?? 0,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'Completed',
            OrderStatus.delivered,
            statusCounts[OrderStatus.delivered] ?? 0,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'Cancelled',
            OrderStatus.cancelled,
            statusCounts[OrderStatus.cancelled] ?? 0,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    String label,
    OrderStatus? status,
    int count,
  ) {
    final isSelected = selectedStatus == status;
    
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected 
                    ? Colors.white 
                    : Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(
                  color: isSelected 
                      ? Theme.of(context).primaryColor 
                      : Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        onStatusChanged(selected ? status : null);
      },
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }
}
