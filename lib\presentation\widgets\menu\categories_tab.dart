import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/data/models/menu.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_state.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_event.dart';
import 'package:supplier_app/presentation/widgets/menu/category_card.dart';
import 'package:supplier_app/presentation/widgets/common/empty_state_widget.dart';
import 'package:supplier_app/presentation/screens/menu/add_category_screen.dart';
import 'package:supplier_app/presentation/widgets/common/loading_widget.dart';

class CategoriesTab extends StatelessWidget {
  const CategoriesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MenuBloc, MenuState>(
      listener: (context, state) {
        if (state is MenuCategoryCreateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Category "${state.category.name}" created successfully'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        } else if (state is MenuCategoryCreateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } else if (state is MenuCategoryUpdateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Category "${state.category.name}" updated successfully'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        } else if (state is MenuCategoryUpdateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } else if (state is MenuCategoryDeleteSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Category deleted successfully'),
            ),
          );
        } else if (state is MenuCategoryDeleteError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is MenuLoading) {
          return const LoadingWidget(message: 'Loading categories...');
        }

        if (state is MenuError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load categories',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    context
                        .read<MenuBloc>()
                        .add(const MenuCategoriesLoadRequested());
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is MenuLoaded) {
          if (state.categories.isEmpty) {
            return const EmptyStateWidget(
              icon: Icons.category_outlined,
              title: 'No Categories',
              message:
                  'Start by creating your first menu category to organize your dishes.',
              actionText: 'Add Category',
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              context.read<MenuBloc>().add(const MenuCategoriesLoadRequested());
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: state.categories.length,
              itemBuilder: (context, index) {
                final category = state.categories[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: CategoryCard(
                    category: category,
                    onEdit: () {
                      _navigateToEditCategory(context, category);
                    },
                    onDelete: () {
                      _showDeleteDialog(context, category.id, category.name);
                    },
                    onToggleActive: (isActive) {
                      final updatedCategory =
                          category.copyWith(isActive: isActive);
                      context.read<MenuBloc>().add(
                            MenuCategoryUpdateRequested(updatedCategory),
                          );
                    },
                  ),
                );
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  void _showDeleteDialog(
      BuildContext context, String categoryId, String categoryName) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text(
          'Are you sure you want to delete "$categoryName"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              context.read<MenuBloc>().add(
                    MenuCategoryDeleteRequested(categoryId),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _navigateToEditCategory(BuildContext context, MenuCategory category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddCategoryScreen(category: category),
      ),
    );
  }

  static void navigateToAddCategory(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddCategoryScreen(),
      ),
    );
  }
}
