import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supplier_app/data/models/user.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Storage keys
  static const String _authTokenKey = 'secure_auth_token';
  static const String _userDataKey = 'secure_user_data';
  static const String _restaurantIdKey = 'secure_restaurant_id';
  static const String _rememberMeKey = 'remember_me_preference';
  static const String _lastLoginKey = 'last_login_timestamp';
  static const String _sessionExpiryKey = 'session_expiry';

  // Store authentication token securely
  Future<void> storeAuthToken(AuthToken token) async {
    try {
      final tokenJson = jsonEncode(token.toJson());
      await _storage.write(key: _authTokenKey, value: tokenJson);

      // Store session expiry
      await _storage.write(
        key: _sessionExpiryKey,
        value: token.expiresAt.toIso8601String(),
      );
    } catch (e) {
      throw SecureStorageException(
          'Failed to store auth token: ${e.toString()}');
    }
  }

  // Retrieve authentication token
  Future<AuthToken?> getAuthToken() async {
    try {
      final tokenJson = await _storage.read(key: _authTokenKey);
      if (tokenJson == null) return null;

      final tokenData = jsonDecode(tokenJson) as Map<String, dynamic>;
      return AuthToken.fromJson(tokenData);
    } catch (e) {
      // If there's an error reading the token, clear it
      await clearAuthToken();
      return null;
    }
  }

  // Store user data securely
  Future<void> storeUserData(User user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _storage.write(key: _userDataKey, value: userJson);

      // Update last login timestamp
      await _storage.write(
        key: _lastLoginKey,
        value: DateTime.now().toIso8601String(),
      );
    } catch (e) {
      throw SecureStorageException(
          'Failed to store user data: ${e.toString()}');
    }
  }

  // Retrieve user data
  Future<User?> getUserData() async {
    try {
      final userJson = await _storage.read(key: _userDataKey);
      if (userJson == null) return null;

      final userData = jsonDecode(userJson) as Map<String, dynamic>;
      return User.fromJson(userData);
    } catch (e) {
      // If there's an error reading user data, clear it
      await clearUserData();
      return null;
    }
  }

  // Store restaurant ID
  Future<void> storeRestaurantId(String restaurantId) async {
    try {
      await _storage.write(key: _restaurantIdKey, value: restaurantId);
    } catch (e) {
      throw SecureStorageException(
          'Failed to store restaurant ID: ${e.toString()}');
    }
  }

  // Retrieve restaurant ID
  Future<String?> getRestaurantId() async {
    try {
      return await _storage.read(key: _restaurantIdKey);
    } catch (e) {
      return null;
    }
  }

  // Store remember me preference
  Future<void> storeRememberMePreference(bool rememberMe) async {
    try {
      // Use SharedPreferences for non-sensitive preference data
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_rememberMeKey, rememberMe);
    } catch (e) {
      // Silently fail for preferences
    }
  }

  // Get remember me preference
  Future<bool> getRememberMePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_rememberMeKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  // Get last login timestamp
  Future<DateTime?> getLastLoginTimestamp() async {
    try {
      final timestampStr = await _storage.read(key: _lastLoginKey);
      if (timestampStr == null) return null;
      return DateTime.parse(timestampStr);
    } catch (e) {
      return null;
    }
  }

  // Check if session is valid
  Future<bool> isSessionValid() async {
    try {
      final token = await getAuthToken().timeout(const Duration(seconds: 3));
      if (token == null) return false;

      // Check if token is expired
      if (token.isExpired) {
        clearAuthData().catchError((_) {}); // Don't wait for clearing
        return false;
      }

      // Check if session has been inactive for too long (30 days)
      final lastLogin =
          await getLastLoginTimestamp().timeout(const Duration(seconds: 2));
      if (lastLogin != null) {
        final daysSinceLogin = DateTime.now().difference(lastLogin).inDays;
        if (daysSinceLogin > 30) {
          clearAuthData().catchError((_) {}); // Don't wait for clearing
          return false;
        }
      }

      return true;
    } catch (e) {
      // Don't clear data on timeout/error, just return false
      return false;
    }
  }

  // Check if user should be remembered
  Future<bool> shouldRememberUser() async {
    try {
      final rememberMe =
          await getRememberMePreference().timeout(const Duration(seconds: 2));
      if (!rememberMe) return false;

      final hasValidSession =
          await isSessionValid().timeout(const Duration(seconds: 3));
      return hasValidSession;
    } catch (e) {
      // On timeout or error, default to false
      return false;
    }
  }

  // Store complete authentication data
  Future<void> storeAuthData({
    required AuthToken token,
    required User user,
    required String restaurantId,
    bool rememberMe = false,
  }) async {
    try {
      await Future.wait([
        storeAuthToken(token),
        storeUserData(user),
        storeRestaurantId(restaurantId),
        storeRememberMePreference(rememberMe),
      ]);
    } catch (e) {
      // If storing fails, clear any partial data
      await clearAuthData();
      rethrow;
    }
  }

  // Clear authentication token
  Future<void> clearAuthToken() async {
    try {
      await Future.wait([
        _storage.delete(key: _authTokenKey),
        _storage.delete(key: _sessionExpiryKey),
      ]);
    } catch (e) {
      // Silently continue with clearing other data
    }
  }

  // Clear user data
  Future<void> clearUserData() async {
    try {
      await _storage.delete(key: _userDataKey);
    } catch (e) {
      // Silently continue
    }
  }

  // Clear restaurant ID
  Future<void> clearRestaurantId() async {
    try {
      await _storage.delete(key: _restaurantIdKey);
    } catch (e) {
      // Silently continue
    }
  }

  // Clear all authentication data
  Future<void> clearAuthData() async {
    try {
      await Future.wait([
        clearAuthToken(),
        clearUserData(),
        clearRestaurantId(),
        _storage.delete(key: _lastLoginKey),
      ]);
    } catch (e) {
      // Even if clearing fails, try to clear everything
      try {
        await _storage.deleteAll();
      } catch (e) {
        // Last resort - silently fail
      }
    }
  }

  // Clear remember me preference (for logout)
  Future<void> clearRememberMePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_rememberMeKey);
    } catch (e) {
      // Silently fail
    }
  }

  // Update last activity timestamp
  Future<void> updateLastActivity() async {
    try {
      await _storage.write(
        key: _lastLoginKey,
        value: DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // Silently fail for activity updates
    }
  }

  // Check if secure storage is available
  Future<bool> isSecureStorageAvailable() async {
    try {
      await _storage.write(key: 'test_key', value: 'test_value');
      await _storage.delete(key: 'test_key');
      return true;
    } catch (e) {
      return false;
    }
  }
}

class SecureStorageException implements Exception {
  final String message;

  const SecureStorageException(this.message);

  @override
  String toString() => 'SecureStorageException: $message';
}
