import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:supplier_app/data/models/menu.dart';

abstract class MenuEvent extends Equatable {
  const MenuEvent();

  @override
  List<Object?> get props => [];
}

// Categories Events
class MenuCategoriesLoadRequested extends MenuEvent {
  const MenuCategoriesLoadRequested();
}

class MenuCategoryCreateRequested extends MenuEvent {
  final MenuCategory category;
  final File? imageFile;

  const MenuCategoryCreateRequested(this.category, [this.imageFile]);

  @override
  List<Object?> get props => [category, imageFile];
}

class MenuCategoryUpdateRequested extends MenuEvent {
  final MenuCategory category;

  const MenuCategoryUpdateRequested(this.category);

  @override
  List<Object> get props => [category];
}

class MenuCategoryDeleteRequested extends MenuEvent {
  final String categoryId;

  const MenuCategoryDeleteRequested(this.categoryId);

  @override
  List<Object> get props => [categoryId];
}

// Dishes Events
class MenuDishesLoadRequested extends MenuEvent {
  final String? categoryId;

  const MenuDishesLoadRequested({this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class MenuDishCreateRequested extends MenuEvent {
  final Dish dish;
  final File? imageFile;

  const MenuDishCreateRequested(this.dish, [this.imageFile]);

  @override
  List<Object?> get props => [dish, imageFile];
}

class MenuDishUpdateRequested extends MenuEvent {
  final Dish dish;

  const MenuDishUpdateRequested(this.dish);

  @override
  List<Object> get props => [dish];
}

class MenuDishDeleteRequested extends MenuEvent {
  final String dishId;

  const MenuDishDeleteRequested(this.dishId);

  @override
  List<Object> get props => [dishId];
}

class MenuDishAvailabilityToggleRequested extends MenuEvent {
  final String dishId;
  final bool isAvailable;

  const MenuDishAvailabilityToggleRequested(this.dishId, this.isAvailable);

  @override
  List<Object> get props => [dishId, isAvailable];
}

// Image Upload Events
class MenuImageUploadRequested extends MenuEvent {
  final File imageFile;

  const MenuImageUploadRequested(this.imageFile);

  @override
  List<Object> get props => [imageFile];
}

// Filter Events
class MenuFilterChanged extends MenuEvent {
  final String? categoryId;

  const MenuFilterChanged({this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}
