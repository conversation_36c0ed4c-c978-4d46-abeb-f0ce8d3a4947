import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/core/services/firebase_auth_service.dart';
import 'package:supplier_app/data/models/user.dart';

class AuthRepository {
  final FirebaseAuthService _firebaseAuthService;

  AuthRepository({FirebaseAuthService? firebaseAuthService})
      : _firebaseAuthService = firebaseAuthService ?? FirebaseAuthService();

  // Login with Firebase
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      // Sign in with Firebase
      final user = await _firebaseAuthService.signInWithEmailAndPassword(
        email: request.email,
        password: request.password,
      );

      // Get Firebase ID token
      final idToken =
          await _firebaseAuthService.currentUser?.getIdToken() ?? '';
      final refreshToken = _firebaseAuthService.currentUser?.refreshToken ?? '';

      // Create auth token
      final authToken = AuthToken(
        accessToken: idToken,
        refreshToken: refreshToken,
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      // Create login response
      final loginResponse = LoginResponse(
        user: user,
        token: authToken,
        restaurantId: user.id, // Using user ID as restaurant ID
      );

      // Store auth data locally
      await _storeAuthData(loginResponse);

      return loginResponse;
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(e.message ?? 'Login failed');
    } catch (e) {
      throw AuthException('Login failed: ${e.toString()}');
    }
  }

  // Logout with Firebase
  Future<void> logout() async {
    try {
      // Sign out from Firebase
      await _firebaseAuthService.signOut();
    } catch (e) {
      // Continue with logout even if Firebase signout fails
    } finally {
      // Clear local auth data
      await _clearAuthData();
    }
  }

  // Refresh Token with Firebase
  Future<AuthToken> refreshToken(String refreshToken) async {
    try {
      // Reload Firebase user to get fresh token
      await _firebaseAuthService.reloadUser();

      // Get new ID token
      final idToken =
          await _firebaseAuthService.currentUser?.getIdToken(true) ?? '';
      final newRefreshToken =
          _firebaseAuthService.currentUser?.refreshToken ?? '';

      final newToken = AuthToken(
        accessToken: idToken,
        refreshToken: newRefreshToken,
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      // Update stored token
      await _storeToken(newToken);

      return newToken;
    } catch (e) {
      throw AuthException('Failed to refresh token: ${e.toString()}');
    }
  }

  // Get current user from Firebase
  Future<User?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuthService.currentUser;
      if (firebaseUser == null) return null;

      // Try to get from local storage first
      final storedUser = await getStoredUser();
      if (storedUser != null) {
        return storedUser;
      }

      // If not in storage, get from Firebase and store
      // This would typically fetch from Firestore
      // For now, return null if not in local storage
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if user is authenticated with Firebase
  Future<bool> isAuthenticated() async {
    // Check Firebase auth state
    final firebaseUser = _firebaseAuthService.currentUser;
    if (firebaseUser == null) {
      await _clearAuthData();
      return false;
    }

    // Check if we have stored auth data
    final token = await getStoredToken();
    if (token == null) return false;

    // If token is expired, try to refresh
    if (token.isExpired) {
      try {
        await refreshToken(token.refreshToken);
        return true;
      } catch (e) {
        await _clearAuthData();
        return false;
      }
    }

    return true;
  }

  // Get stored token
  Future<AuthToken?> getStoredToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tokenJson = prefs.getString(AppConstants.authTokenKey);
      if (tokenJson != null) {
        final data = jsonDecode(tokenJson) as Map<String, dynamic>;
        return AuthToken.fromJson(data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get stored user
  Future<User?> getStoredUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userDataKey);
      if (userJson != null) {
        final data = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get stored restaurant ID
  Future<String?> getStoredRestaurantId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(AppConstants.restaurantDataKey);
    } catch (e) {
      return null;
    }
  }

  // Private methods
  Future<void> _storeAuthData(LoginResponse loginResponse) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.authTokenKey,
      jsonEncode(loginResponse.token.toJson()),
    );
    await prefs.setString(
      AppConstants.userDataKey,
      jsonEncode(loginResponse.user.toJson()),
    );
    await prefs.setString(
      AppConstants.restaurantDataKey,
      loginResponse.restaurantId,
    );
  }

  Future<void> _storeToken(AuthToken token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.authTokenKey,
      jsonEncode(token.toJson()),
    );
  }

  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.authTokenKey);
    await prefs.remove(AppConstants.userDataKey);
    await prefs.remove(AppConstants.restaurantDataKey);
  }

  void dispose() {
    // No resources to dispose for Firebase
  }
}

class AuthException implements Exception {
  final String message;

  const AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
