import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/core/services/firebase_auth_service.dart';
import 'package:supplier_app/core/services/secure_storage_service.dart';
import 'package:supplier_app/core/services/connectivity_service.dart';
import 'package:supplier_app/data/models/user.dart';

class AuthRepository {
  final FirebaseAuthService _firebaseAuthService;
  final SecureStorageService _secureStorage;
  final ConnectivityService _connectivityService;

  AuthRepository({
    FirebaseAuthService? firebaseAuthService,
    SecureStorageService? secureStorageService,
    ConnectivityService? connectivityService,
  })  : _firebaseAuthService = firebaseAuthService ?? FirebaseAuthService(),
        _secureStorage = secureStorageService ?? SecureStorageService(),
        _connectivityService = connectivityService ?? ConnectivityService();

  // Login with Firebase
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      // Check connectivity before attempting login
      if (!_connectivityService.isConnected) {
        final connected = await _connectivityService.waitForConnection();
        if (!connected) {
          throw AuthException('No internet connection available');
        }
      }

      // Sign in with Firebase
      final user = await _firebaseAuthService.signInWithEmailAndPassword(
        email: request.email,
        password: request.password,
      );

      // Get Firebase ID token
      final idToken =
          await _firebaseAuthService.currentUser?.getIdToken() ?? '';
      final refreshToken = _firebaseAuthService.currentUser?.refreshToken ?? '';

      // Create auth token with longer expiry for persistent sessions
      final authToken = AuthToken(
        accessToken: idToken,
        refreshToken: refreshToken,
        expiresAt: DateTime.now()
            .add(const Duration(hours: 24)), // Extended for persistence
      );

      // Create login response
      final loginResponse = LoginResponse(
        user: user,
        token: authToken,
        restaurantId: user.id, // Using user ID as restaurant ID
      );

      // Store auth data securely
      await _storeAuthDataSecurely(loginResponse, request.rememberMe);

      return loginResponse;
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(e.message ?? 'Login failed');
    } catch (e) {
      throw AuthException('Login failed: ${e.toString()}');
    }
  }

  // Logout with Firebase
  Future<void> logout({bool clearRememberMe = false}) async {
    try {
      // Sign out from Firebase
      await _firebaseAuthService.signOut();
    } catch (e) {
      // Continue with logout even if Firebase signout fails
    } finally {
      // Clear secure auth data
      await _clearAuthDataSecurely(clearRememberMe: clearRememberMe);
    }
  }

  // Refresh Token with Firebase
  Future<AuthToken> refreshToken(String refreshToken) async {
    try {
      // Reload Firebase user to get fresh token
      await _firebaseAuthService.reloadUser();

      // Get new ID token
      final idToken =
          await _firebaseAuthService.currentUser?.getIdToken(true) ?? '';
      final newRefreshToken =
          _firebaseAuthService.currentUser?.refreshToken ?? '';

      final newToken = AuthToken(
        accessToken: idToken,
        refreshToken: newRefreshToken,
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      // Update stored token securely
      await _storeTokenSecurely(newToken);

      return newToken;
    } catch (e) {
      throw AuthException('Failed to refresh token: ${e.toString()}');
    }
  }

  // Get current user from Firebase
  Future<User?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuthService.currentUser;
      if (firebaseUser == null) return null;

      // Try to get from local storage first
      final storedUser = await getStoredUser();
      if (storedUser != null) {
        return storedUser;
      }

      // If not in storage, get from Firebase and store
      // This would typically fetch from Firestore
      // For now, return null if not in local storage
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if user is authenticated with Firebase and persistent session
  Future<bool> isAuthenticated() async {
    try {
      debugPrint('🔍 AuthRepository: Starting authentication check...');

      // Quick Firebase user check first
      final firebaseUser = _firebaseAuthService.currentUser;
      debugPrint(
          '🔍 AuthRepository: Firebase user: ${firebaseUser?.email ?? 'null'}');

      if (firebaseUser == null) {
        debugPrint('❌ AuthRepository: No Firebase user, returning false');
        return false;
      }

      // Firebase user exists, now check if session should be persistent
      try {
        debugPrint('🔍 AuthRepository: Checking remember me preference...');
        final rememberMe =
            await _secureStorage.getRememberMePreference().timeout(
                  const Duration(seconds: 1),
                  onTimeout: () => false,
                );

        if (!rememberMe) {
          debugPrint('❌ AuthRepository: User chose not to be remembered');
          return false;
        }

        debugPrint('🔍 AuthRepository: Checking stored token...');
        final token = await _secureStorage.getAuthToken().timeout(
              const Duration(seconds: 1),
              onTimeout: () => null,
            );

        if (token == null) {
          debugPrint('❌ AuthRepository: No stored token found');
          return false;
        }

        if (token.isExpired) {
          debugPrint('❌ AuthRepository: Token is expired');
          return false;
        }

        debugPrint(
            '✅ AuthRepository: User is authenticated with persistent session');
        return true;
      } catch (e) {
        debugPrint(
            '⚠️ AuthRepository: Secure storage error, falling back to basic auth: $e');
        // If secure storage fails, still allow basic Firebase auth
        return true;
      }
    } catch (e) {
      debugPrint('❌ AuthRepository: Authentication check error: $e');
      return false;
    }
  }

  // Get stored token from secure storage
  Future<AuthToken?> getStoredToken() async {
    try {
      return await _secureStorage.getAuthToken();
    } catch (e) {
      return null;
    }
  }

  // Get stored user with secure storage fallback
  Future<User?> getStoredUser() async {
    try {
      // Try to get user from secure storage first
      try {
        final storedUser = await _secureStorage.getUserData().timeout(
              const Duration(seconds: 1),
            );
        if (storedUser != null) {
          debugPrint('✅ AuthRepository: Retrieved user from secure storage');
          return storedUser;
        }
      } catch (e) {
        debugPrint('⚠️ AuthRepository: Secure storage user fetch failed: $e');
      }

      // Fallback: Create user from Firebase user
      final firebaseUser = _firebaseAuthService.currentUser;
      if (firebaseUser == null) return null;

      debugPrint('🔄 AuthRepository: Creating user from Firebase data');
      final user = User(
        id: firebaseUser.uid,
        email: firebaseUser.email ?? '',
        name: firebaseUser.displayName ?? 'Supplier',
        phone: firebaseUser.phoneNumber ?? '',
        profileImageUrl: firebaseUser.photoURL,
        role: 'owner', // Default role for suppliers
        permissions: const [
          'manage_orders',
          'manage_menu',
          'manage_promotions'
        ],
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Try to store user in secure storage for next time (don't wait)
      _secureStorage.storeUserData(user).catchError((e) {
        debugPrint('⚠️ AuthRepository: Failed to store user data: $e');
      });

      return user;
    } catch (e) {
      debugPrint('❌ AuthRepository: Error getting stored user: $e');
      return null;
    }
  }

  // Get stored restaurant ID with secure storage fallback
  Future<String?> getStoredRestaurantId() async {
    try {
      // Try to get restaurant ID from secure storage first
      try {
        final storedRestaurantId =
            await _secureStorage.getRestaurantId().timeout(
                  const Duration(seconds: 1),
                );
        if (storedRestaurantId != null) {
          debugPrint(
              '✅ AuthRepository: Retrieved restaurant ID from secure storage');
          return storedRestaurantId;
        }
      } catch (e) {
        debugPrint(
            '⚠️ AuthRepository: Secure storage restaurant ID fetch failed: $e');
      }

      // Fallback: Use Firebase user ID as restaurant ID
      final firebaseUser = _firebaseAuthService.currentUser;
      final restaurantId = firebaseUser?.uid;

      if (restaurantId != null) {
        // Try to store restaurant ID in secure storage for next time (don't wait)
        _secureStorage.storeRestaurantId(restaurantId).catchError((e) {
          debugPrint('⚠️ AuthRepository: Failed to store restaurant ID: $e');
        });
      }

      return restaurantId;
    } catch (e) {
      debugPrint('❌ AuthRepository: Error getting restaurant ID: $e');
      return null;
    }
  }

  // Private methods for secure storage
  Future<void> _storeAuthDataSecurely(
      LoginResponse loginResponse, bool rememberMe) async {
    try {
      await _secureStorage.storeAuthData(
        token: loginResponse.token,
        user: loginResponse.user,
        restaurantId: loginResponse.restaurantId,
        rememberMe: rememberMe,
      );
    } catch (e) {
      throw AuthException(
          'Failed to store authentication data: ${e.toString()}');
    }
  }

  Future<void> _storeTokenSecurely(AuthToken token) async {
    try {
      await _secureStorage.storeAuthToken(token);
    } catch (e) {
      throw AuthException('Failed to store token: ${e.toString()}');
    }
  }

  Future<void> _clearAuthDataSecurely({bool clearRememberMe = false}) async {
    try {
      await _secureStorage.clearAuthData();
      if (clearRememberMe) {
        await _secureStorage.clearRememberMePreference();
      }
    } catch (e) {
      // Continue with clearing even if secure storage fails
      try {
        // Fallback to SharedPreferences clearing
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(AppConstants.authTokenKey);
        await prefs.remove(AppConstants.userDataKey);
        await prefs.remove(AppConstants.restaurantDataKey);
      } catch (e) {
        // Silently fail if all clearing methods fail
      }
    }
  }

  void dispose() {
    // No resources to dispose for Firebase
  }
}

class AuthException implements Exception {
  final String message;

  const AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
