import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/data/models/order.dart';
import 'package:supplier_app/data/repositories/order_repository.dart';
import 'orders_event.dart';
import 'orders_state.dart';

class OrdersBloc extends Bloc<OrdersEvent, OrdersState> {
  final OrderRepository orderRepository;

  OrdersBloc({required this.orderRepository}) : super(const OrdersInitial()) {
    on<OrdersLoadRequested>(_onOrdersLoadRequested);
    on<OrdersLoadMoreRequested>(_onOrdersLoadMoreRequested);
    on<OrderDetailsRequested>(_onOrderDetailsRequested);
    on<OrderAcceptRequested>(_onOrderAcceptRequested);
    on<OrderRejectRequested>(_onOrderRejectRequested);
    on<OrderStatusUpdateRequested>(_onOrderStatusUpdateRequested);
    on<OrdersFilterChanged>(_onOrdersFilterChanged);
  }

  Future<void> _onOrdersLoadRequested(
    OrdersLoadRequested event,
    Emitter<OrdersState> emit,
  ) async {
    if (event.isRefresh && state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;
      emit(currentState.copyWith(isRefreshing: true));
    } else {
      emit(const OrdersLoading());
    }

    try {
      final orders = await orderRepository.getOrders(
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
        page: 1,
        limit: AppConstants.defaultPageSize,
      );

      emit(OrdersLoaded(
        orders: orders,
        currentFilter: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
        hasReachedMax: orders.length < AppConstants.defaultPageSize,
        currentPage: 1,
      ));
    } catch (e) {
      emit(OrdersError(e.toString()));
    }
  }

  Future<void> _onOrdersLoadMoreRequested(
    OrdersLoadMoreRequested event,
    Emitter<OrdersState> emit,
  ) async {
    if (state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;

      if (currentState.hasReachedMax || currentState.isLoadingMore) {
        return;
      }

      emit(currentState.copyWith(isLoadingMore: true));

      try {
        final nextPage = currentState.currentPage + 1;
        final newOrders = await orderRepository.getOrders(
          status: event.status,
          startDate: event.startDate,
          endDate: event.endDate,
          page: nextPage,
          limit: AppConstants.defaultPageSize,
        );

        emit(currentState.copyWith(
          orders: [...currentState.orders, ...newOrders],
          hasReachedMax: newOrders.length < AppConstants.defaultPageSize,
          isLoadingMore: false,
          currentPage: nextPage,
        ));
      } catch (e) {
        emit(currentState.copyWith(isLoadingMore: false));
        emit(OrdersError(e.toString()));
      }
    }
  }

  Future<void> _onOrderDetailsRequested(
    OrderDetailsRequested event,
    Emitter<OrdersState> emit,
  ) async {
    emit(OrderDetailsLoading(event.orderId));

    try {
      final order = await orderRepository.getOrderById(event.orderId);
      if (order != null) {
        emit(OrderDetailsLoaded(order));
      } else {
        emit(OrderDetailsError('Order not found', event.orderId));
      }
    } catch (e) {
      emit(OrderDetailsError(e.toString(), event.orderId));
    }
  }

  Future<void> _onOrderAcceptRequested(
    OrderAcceptRequested event,
    Emitter<OrdersState> emit,
  ) async {
    emit(OrderUpdating(event.orderId, OrderStatus.accepted));

    try {
      final updatedOrder = await orderRepository.acceptOrder(
        event.orderId,
        estimatedPreparationTime: event.estimatedPreparationTime,
      );

      emit(OrderUpdateSuccess(updatedOrder));

      // Update the orders list if currently loaded
      if (state is OrdersLoaded) {
        _updateOrderInList(updatedOrder, emit);
      }
    } catch (e) {
      emit(OrderUpdateError(e.toString(), event.orderId));
    }
  }

  Future<void> _onOrderRejectRequested(
    OrderRejectRequested event,
    Emitter<OrdersState> emit,
  ) async {
    emit(OrderUpdating(event.orderId, OrderStatus.rejected));

    try {
      final updatedOrder = await orderRepository.rejectOrder(
        event.orderId,
        event.reason,
      );

      emit(OrderUpdateSuccess(updatedOrder));

      // Update the orders list if currently loaded
      if (state is OrdersLoaded) {
        _updateOrderInList(updatedOrder, emit);
      }
    } catch (e) {
      emit(OrderUpdateError(e.toString(), event.orderId));
    }
  }

  Future<void> _onOrderStatusUpdateRequested(
    OrderStatusUpdateRequested event,
    Emitter<OrdersState> emit,
  ) async {
    emit(OrderUpdating(event.orderId, event.status));

    try {
      final updatedOrder = await orderRepository.updateOrderStatus(
        event.orderId,
        event.status,
      );

      emit(OrderUpdateSuccess(updatedOrder));

      // Update the orders list if currently loaded
      if (state is OrdersLoaded) {
        _updateOrderInList(updatedOrder, emit);
      }
    } catch (e) {
      emit(OrderUpdateError(e.toString(), event.orderId));
    }
  }

  Future<void> _onOrdersFilterChanged(
    OrdersFilterChanged event,
    Emitter<OrdersState> emit,
  ) async {
    // Reload orders with new filter
    add(OrdersLoadRequested(
      status: event.status,
      startDate: event.startDate,
      endDate: event.endDate,
    ));
  }

  void _updateOrderInList(Order updatedOrder, Emitter<OrdersState> emit) {
    if (state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;
      final updatedOrders = currentState.orders.map((order) {
        return order.id == updatedOrder.id ? updatedOrder : order;
      }).toList();

      emit(currentState.copyWith(orders: updatedOrders));
    }
  }
}
