// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCOmvUApwkGo0s5u9PBya6tGscm1rYsWRk',
    appId: '1:672277532313:web:d26e14927a03043bf5b208',
    messagingSenderId: '672277532313',
    projectId: 'foodie-33530',
    authDomain: 'foodie-33530.firebaseapp.com',
    storageBucket: 'foodie-33530.firebasestorage.app',
    measurementId: 'G-LZF72H1LT6',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDkUtoSgTvVSzR8v_PhYamqtjwPc2tLj4A',
    appId: '1:672277532313:android:bf514acfcfdc2d06f5b208',
    messagingSenderId: '672277532313',
    projectId: 'foodie-33530',
    storageBucket: 'foodie-33530.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCUTxN9z5k01-wc6MOzBGprcYjdSY_AoOg',
    appId: '1:672277532313:ios:8a1c9a0687f4b089f5b208',
    messagingSenderId: '672277532313',
    projectId: 'foodie-33530',
    storageBucket: 'foodie-33530.firebasestorage.app',
    androidClientId: '672277532313-2aprqe3mtak2riqest2lc595e4mvftgh.apps.googleusercontent.com',
    iosClientId: '672277532313-u1e6i8kdcg2kc543jqggo8u8m46l4elk.apps.googleusercontent.com',
    iosBundleId: 'com.example.supplierApp',
  );
}
