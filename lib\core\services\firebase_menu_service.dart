import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:supplier_app/data/models/menu.dart';

class FirebaseMenuService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collections
  static const String _categoriesCollection = 'menu_categories';
  static const String _dishesCollection = 'dishes';

  // CATEGORIES CRUD OPERATIONS

  // Get categories stream
  Stream<List<MenuCategory>> getCategoriesStream(String restaurantId) {
    return _firestore
        .collection(_categoriesCollection)
        .where('restaurantId', isEqualTo: restaurantId)
        .where('isActive', isEqualTo: true)
        .orderBy('sortOrder')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return MenuCategory.fromJson(data);
      }).toList();
    });
  }

  // Get categories
  Future<List<MenuCategory>> getCategories(String restaurantId) async {
    try {
      final snapshot = await _firestore
          .collection(_categoriesCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return MenuCategory.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching categories: $e');
      throw Exception('Failed to fetch categories: $e');
    }
  }

  // Create category
  Future<MenuCategory> createCategory(MenuCategory category) async {
    try {
      final categoryData = category.toJson();
      categoryData.remove('id'); // Remove ID for creation
      categoryData['createdAt'] = FieldValue.serverTimestamp();
      categoryData['updatedAt'] = FieldValue.serverTimestamp();

      final docRef = await _firestore.collection(_categoriesCollection).add(categoryData);
      
      // Return category with generated ID
      return category.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('Error creating category: $e');
      throw Exception('Failed to create category: $e');
    }
  }

  // Update category
  Future<MenuCategory> updateCategory(MenuCategory category) async {
    try {
      final categoryData = category.toJson();
      categoryData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(_categoriesCollection).doc(category.id).update(categoryData);
      return category;
    } catch (e) {
      debugPrint('Error updating category: $e');
      throw Exception('Failed to update category: $e');
    }
  }

  // Delete category
  Future<void> deleteCategory(String categoryId) async {
    try {
      // Soft delete by setting isActive to false
      await _firestore.collection(_categoriesCollection).doc(categoryId).update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error deleting category: $e');
      throw Exception('Failed to delete category: $e');
    }
  }

  // DISHES CRUD OPERATIONS

  // Get dishes stream
  Stream<List<Dish>> getDishesStream(String restaurantId, {String? categoryId}) {
    Query query = _firestore
        .collection(_dishesCollection)
        .where('restaurantId', isEqualTo: restaurantId)
        .orderBy('sortOrder');

    if (categoryId != null) {
      query = query.where('categoryId', isEqualTo: categoryId);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Dish.fromJson(data);
      }).toList();
    });
  }

  // Get dishes
  Future<List<Dish>> getDishes(String restaurantId, {String? categoryId}) async {
    try {
      Query query = _firestore
          .collection(_dishesCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .orderBy('sortOrder');

      if (categoryId != null) {
        query = query.where('categoryId', isEqualTo: categoryId);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Dish.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error fetching dishes: $e');
      throw Exception('Failed to fetch dishes: $e');
    }
  }

  // Get single dish
  Future<Dish?> getDish(String dishId) async {
    try {
      final doc = await _firestore.collection(_dishesCollection).doc(dishId).get();
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      return Dish.fromJson(data);
    } catch (e) {
      debugPrint('Error fetching dish: $e');
      throw Exception('Failed to fetch dish: $e');
    }
  }

  // Create dish
  Future<Dish> createDish(Dish dish) async {
    try {
      final dishData = dish.toJson();
      dishData.remove('id'); // Remove ID for creation
      dishData['createdAt'] = FieldValue.serverTimestamp();
      dishData['updatedAt'] = FieldValue.serverTimestamp();

      final docRef = await _firestore.collection(_dishesCollection).add(dishData);
      
      // Return dish with generated ID
      return dish.copyWith(id: docRef.id);
    } catch (e) {
      debugPrint('Error creating dish: $e');
      throw Exception('Failed to create dish: $e');
    }
  }

  // Update dish
  Future<Dish> updateDish(Dish dish) async {
    try {
      final dishData = dish.toJson();
      dishData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(_dishesCollection).doc(dish.id).update(dishData);
      return dish;
    } catch (e) {
      debugPrint('Error updating dish: $e');
      throw Exception('Failed to update dish: $e');
    }
  }

  // Toggle dish availability
  Future<void> toggleDishAvailability(String dishId, bool isAvailable) async {
    try {
      await _firestore.collection(_dishesCollection).doc(dishId).update({
        'isAvailable': isAvailable,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error toggling dish availability: $e');
      throw Exception('Failed to toggle dish availability: $e');
    }
  }

  // Delete dish
  Future<void> deleteDish(String dishId) async {
    try {
      await _firestore.collection(_dishesCollection).doc(dishId).delete();
    } catch (e) {
      debugPrint('Error deleting dish: $e');
      throw Exception('Failed to delete dish: $e');
    }
  }

  // IMAGE UPLOAD OPERATIONS

  // Upload image to Firebase Storage
  Future<String> uploadImage(File imageFile, String restaurantId, {String? dishId, String? categoryId}) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}';
      final path = dishId != null 
          ? 'restaurants/$restaurantId/dishes/$dishId/$fileName'
          : 'restaurants/$restaurantId/categories/$categoryId/$fileName';

      final ref = _storage.ref().child(path);
      final uploadTask = ref.putFile(imageFile);
      
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading image: $e');
      throw Exception('Failed to upload image: $e');
    }
  }

  // Delete image from Firebase Storage
  Future<void> deleteImage(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Don't throw error for image deletion as it's not critical
    }
  }

  // UTILITY METHODS

  // Get next sort order for category
  Future<int> getNextCategorySortOrder(String restaurantId) async {
    try {
      final snapshot = await _firestore
          .collection(_categoriesCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .orderBy('sortOrder', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) return 1;
      
      final lastSortOrder = snapshot.docs.first.data()['sortOrder'] as int? ?? 0;
      return lastSortOrder + 1;
    } catch (e) {
      return 1;
    }
  }

  // Get next sort order for dish
  Future<int> getNextDishSortOrder(String restaurantId, String categoryId) async {
    try {
      final snapshot = await _firestore
          .collection(_dishesCollection)
          .where('restaurantId', isEqualTo: restaurantId)
          .where('categoryId', isEqualTo: categoryId)
          .orderBy('sortOrder', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) return 1;
      
      final lastSortOrder = snapshot.docs.first.data()['sortOrder'] as int? ?? 0;
      return lastSortOrder + 1;
    } catch (e) {
      return 1;
    }
  }
}
