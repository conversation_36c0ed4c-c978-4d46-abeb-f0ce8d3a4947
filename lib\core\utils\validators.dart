class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    
    return null;
  }

  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  // Phone number validation
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  // Price validation
  static String? validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Price is required';
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'Please enter a valid price';
    }
    
    if (price < 0) {
      return 'Price cannot be negative';
    }
    
    if (price > 9999.99) {
      return 'Price cannot exceed \$9999.99';
    }
    
    return null;
  }

  // Integer validation
  static String? validateInteger(String? value, String fieldName, {int? min, int? max}) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    
    final intValue = int.tryParse(value);
    if (intValue == null) {
      return 'Please enter a valid number';
    }
    
    if (min != null && intValue < min) {
      return '$fieldName must be at least $min';
    }
    
    if (max != null && intValue > max) {
      return '$fieldName cannot exceed $max';
    }
    
    return null;
  }

  // Text length validation
  static String? validateLength(String? value, String fieldName, {int? min, int? max}) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    
    if (min != null && value.length < min) {
      return '$fieldName must be at least $min characters';
    }
    
    if (max != null && value.length > max) {
      return '$fieldName cannot exceed $max characters';
    }
    
    return null;
  }

  // URL validation
  static String? validateUrl(String? value) {
    if (value == null || value.isEmpty) {
      return null; // URL is optional
    }
    
    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$'
    );
    
    if (!urlRegex.hasMatch(value)) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }

  // Percentage validation
  static String? validatePercentage(String? value) {
    if (value == null || value.isEmpty) {
      return 'Percentage is required';
    }
    
    final percentage = double.tryParse(value);
    if (percentage == null) {
      return 'Please enter a valid percentage';
    }
    
    if (percentage < 0 || percentage > 100) {
      return 'Percentage must be between 0 and 100';
    }
    
    return null;
  }

  // Time validation (HH:mm format)
  static String? validateTime(String? value) {
    if (value == null || value.isEmpty) {
      return 'Time is required';
    }
    
    final timeRegex = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
    if (!timeRegex.hasMatch(value)) {
      return 'Please enter time in HH:mm format';
    }
    
    return null;
  }

  // Promo code validation
  static String? validatePromoCode(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Promo code is optional
    }
    
    if (value.length < 3) {
      return 'Promo code must be at least 3 characters';
    }
    
    if (value.length > 20) {
      return 'Promo code cannot exceed 20 characters';
    }
    
    final promoRegex = RegExp(r'^[A-Z0-9]+$');
    if (!promoRegex.hasMatch(value.toUpperCase())) {
      return 'Promo code can only contain letters and numbers';
    }
    
    return null;
  }

  // Combine multiple validators
  static String? Function(String?) combineValidators(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }
}
