import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/data/models/menu.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_bloc.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_event.dart';
import 'package:supplier_app/presentation/bloc/menu/menu_state.dart';

class AddDishScreen extends StatefulWidget {
  final Dish? dish; // For editing existing dish
  final String? preSelectedCategoryId;

  const AddDishScreen({
    super.key,
    this.dish,
    this.preSelectedCategoryId,
  });

  @override
  State<AddDishScreen> createState() => _AddDishScreenState();
}

class _AddDishScreenState extends State<AddDishScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _preparationTimeController = TextEditingController();
  final _allergensController = TextEditingController();
  final _ingredientsController = TextEditingController();
  
  File? _selectedImage;
  String? _existingImageUrl;
  String? _selectedCategoryId;
  DishType _selectedType = DishType.main;
  bool _isAvailable = true;
  bool _isPopular = false;
  bool _isLoading = false;

  List<MenuCategory> _categories = [];

  bool get isEditing => widget.dish != null;

  @override
  void initState() {
    super.initState();
    _selectedCategoryId = widget.preSelectedCategoryId;
    if (isEditing) {
      _initializeForEditing();
    }
    // Load categories
    context.read<MenuBloc>().add(const MenuCategoriesLoadRequested());
  }

  void _initializeForEditing() {
    final dish = widget.dish!;
    _nameController.text = dish.name;
    _descriptionController.text = dish.description;
    _priceController.text = dish.price.toString();
    _preparationTimeController.text = dish.preparationTime.toString();
    _allergensController.text = dish.allergens.join(', ');
    _ingredientsController.text = dish.ingredients.join(', ');
    _existingImageUrl = dish.imageUrl;
    _selectedCategoryId = dish.categoryId;
    _selectedType = dish.type;
    _isAvailable = dish.isAvailable;
    _isPopular = dish.isPopular;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _preparationTimeController.dispose();
    _allergensController.dispose();
    _ingredientsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Dish' : 'Add Dish'),
        actions: [
          BlocConsumer<MenuBloc, MenuState>(
            listener: (context, state) {
              if (state is MenuDishCreateSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Dish created successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
                Navigator.of(context).pop();
              } else if (state is MenuLoaded) {
                setState(() {
                  _categories = state.categories;
                });
              } else if (state is MenuError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            builder: (context, state) {
              final isLoading = state is MenuDishCreating || _isLoading;
              
              return TextButton(
                onPressed: isLoading ? null : _saveDish,
                child: isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(isEditing ? 'Update' : 'Save'),
              );
            },
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image Section
              _buildImageSection(),
              const SizedBox(height: 24),

              // Basic Information
              _buildBasicInfoSection(),
              const SizedBox(height: 24),

              // Category and Type
              _buildCategoryTypeSection(),
              const SizedBox(height: 24),

              // Additional Details
              _buildAdditionalDetailsSection(),
              const SizedBox(height: 24),

              // Settings
              _buildSettingsSection(),
              const SizedBox(height: 24),

              // Help Text
              _buildHelpSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dish Image',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _selectedImage != null
              ? _buildSelectedImage()
              : _existingImageUrl != null
                  ? _buildExistingImage()
                  : _buildImagePlaceholder(),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.photo_library),
                label: const Text('Choose Image'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _takePhoto,
                icon: const Icon(Icons.camera_alt),
                label: const Text('Take Photo'),
              ),
            ),
          ],
        ),
        if (_selectedImage != null || _existingImageUrl != null) ...[
          const SizedBox(height: 8),
          TextButton.icon(
            onPressed: _removeImage,
            icon: const Icon(Icons.delete, color: Colors.red),
            label: const Text('Remove Image', style: TextStyle(color: Colors.red)),
          ),
        ],
      ],
    );
  }

  Widget _buildSelectedImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.file(
        _selectedImage!,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
      ),
    );
  }

  Widget _buildExistingImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        _existingImageUrl!,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return const Center(child: CircularProgressIndicator());
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildImagePlaceholder();
        },
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.restaurant_menu_outlined,
          size: 64,
          color: Colors.grey[400],
        ),
        const SizedBox(height: 8),
        Text(
          'Add dish image',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Recommended: 400x300 pixels',
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Basic Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        
        // Dish Name
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Dish Name *',
            hintText: 'e.g., Grilled Chicken Breast',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Dish name is required';
            }
            if (value.trim().length < 2) {
              return 'Dish name must be at least 2 characters';
            }
            return null;
          },
          textCapitalization: TextCapitalization.words,
        ),
        const SizedBox(height: 16),

        // Description
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description *',
            hintText: 'Describe the dish, ingredients, and preparation',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Description is required';
            }
            if (value.trim().length < 10) {
              return 'Description must be at least 10 characters';
            }
            return null;
          },
          textCapitalization: TextCapitalization.sentences,
        ),
        const SizedBox(height: 16),

        // Price and Preparation Time
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price *',
                  hintText: '0.00',
                  prefixText: '\$ ',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Price is required';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return 'Enter a valid price';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _preparationTimeController,
                decoration: const InputDecoration(
                  labelText: 'Prep Time *',
                  hintText: '15',
                  suffixText: 'min',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Preparation time is required';
                  }
                  final time = int.tryParse(value);
                  if (time == null || time <= 0) {
                    return 'Enter valid time';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category & Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),

        // Category Dropdown
        DropdownButtonFormField<String>(
          value: _selectedCategoryId,
          decoration: const InputDecoration(
            labelText: 'Category *',
            border: OutlineInputBorder(),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category.id,
              child: Text(category.name),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a category';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Dish Type
        DropdownButtonFormField<DishType>(
          value: _selectedType,
          decoration: const InputDecoration(
            labelText: 'Dish Type *',
            border: OutlineInputBorder(),
          ),
          items: DishType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type.displayName),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildAdditionalDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Details',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),

        // Allergens
        TextFormField(
          controller: _allergensController,
          decoration: const InputDecoration(
            labelText: 'Allergens',
            hintText: 'e.g., Nuts, Dairy, Gluten (comma separated)',
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.words,
        ),
        const SizedBox(height: 16),

        // Ingredients
        TextFormField(
          controller: _ingredientsController,
          decoration: const InputDecoration(
            labelText: 'Main Ingredients',
            hintText: 'e.g., Chicken, Rice, Vegetables (comma separated)',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          textCapitalization: TextCapitalization.words,
        ),
      ],
    );
  }

  Widget _buildSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Settings',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),

        Card(
          child: Column(
            children: [
              SwitchListTile(
                title: const Text('Available'),
                subtitle: const Text('Dish is available for ordering'),
                value: _isAvailable,
                onChanged: (value) {
                  setState(() {
                    _isAvailable = value;
                  });
                },
              ),
              const Divider(height: 1),
              SwitchListTile(
                title: const Text('Popular'),
                subtitle: const Text('Mark as popular dish'),
                value: _isPopular,
                onChanged: (value) {
                  setState(() {
                    _isPopular = value;
                  });
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHelpSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Tips for Great Dishes',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '• Use high-quality, appetizing photos\n'
            '• Write detailed, mouth-watering descriptions\n'
            '• Set competitive but profitable prices\n'
            '• Include accurate preparation times\n'
            '• List all allergens for customer safety\n'
            '• Mark popular dishes to boost sales',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 600,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _existingImageUrl = null;
      });
    }
  }

  Future<void> _takePhoto() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.camera,
      maxWidth: 800,
      maxHeight: 600,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _existingImageUrl = null;
      });
    }
  }

  void _removeImage() {
    setState(() {
      _selectedImage = null;
      _existingImageUrl = null;
    });
  }

  Future<void> _saveDish() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCategoryId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a category'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final allergens = _allergensController.text
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();
      final ingredients = _ingredientsController.text
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      if (isEditing) {
        // Update existing dish
        final updatedDish = widget.dish!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.parse(_priceController.text),
          preparationTime: int.parse(_preparationTimeController.text),
          categoryId: _selectedCategoryId!,
          type: _selectedType,
          allergens: allergens,
          ingredients: ingredients,
          isAvailable: _isAvailable,
          isPopular: _isPopular,
          updatedAt: now,
        );

        context.read<MenuBloc>().add(
          MenuDishUpdateRequested(updatedDish),
        );

        // Handle image upload separately if new image is selected
        if (_selectedImage != null) {
          context.read<MenuBloc>().add(
            MenuImageUploadRequested(
              imageFile: _selectedImage!,
              dishId: updatedDish.id,
            ),
          );
        }
      } else {
        // Create new dish
        final dish = Dish(
          id: '', // Will be set by Firebase
          categoryId: _selectedCategoryId!,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.parse(_priceController.text),
          preparationTime: int.parse(_preparationTimeController.text),
          type: _selectedType,
          allergens: allergens,
          ingredients: ingredients,
          isAvailable: _isAvailable,
          isPopular: _isPopular,
          sortOrder: 0, // Will be set by repository
          createdAt: now,
          updatedAt: now,
        );

        context.read<MenuBloc>().add(
          MenuDishCreateRequested(dish, _selectedImage),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
