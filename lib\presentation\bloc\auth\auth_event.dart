import 'package:equatable/equatable.dart';
import 'package:supplier_app/data/models/user.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object> get props => [email, password, rememberMe];
}

class AuthRegisterRequested extends AuthEvent {
  final String email;
  final String password;
  final String name;
  final String restaurantName;
  final String phoneNumber;

  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.name,
    required this.restaurantName,
    required this.phoneNumber,
  });

  @override
  List<Object> get props =>
      [email, password, name, restaurantName, phoneNumber];
}

class AuthLogoutRequested extends AuthEvent {
  final bool? clearRememberMe;

  const AuthLogoutRequested({this.clearRememberMe});

  @override
  List<Object?> get props => [clearRememberMe];
}

class AuthTokenRefreshRequested extends AuthEvent {
  const AuthTokenRefreshRequested();
}

class AuthUserUpdated extends AuthEvent {
  final User user;

  const AuthUserUpdated(this.user);

  @override
  List<Object> get props => [user];
}
