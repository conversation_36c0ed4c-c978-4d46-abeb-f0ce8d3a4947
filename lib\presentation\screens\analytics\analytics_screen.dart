import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_event.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_state.dart';
import 'package:supplier_app/presentation/widgets/analytics/sales_report_tab.dart';
import 'package:supplier_app/presentation/widgets/analytics/order_trends_tab.dart';
import 'package:supplier_app/presentation/widgets/analytics/popular_dishes_tab.dart';
import 'package:supplier_app/presentation/widgets/analytics/customer_feedback_tab.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Load initial analytics data
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    final bloc = context.read<AnalyticsBloc>();
    bloc.add(AnalyticsSalesReportRequested(
      type: ReportType.monthly,
      startDate: _startDate,
      endDate: _endDate,
    ));
    bloc.add(AnalyticsOrderTrendsRequested(
      startDate: _startDate,
      endDate: _endDate,
    ));
    bloc.add(AnalyticsPopularDishesRequested(
      startDate: _startDate,
      endDate: _endDate,
    ));
    bloc.add(AnalyticsCustomerFeedbackRequested(
      startDate: _startDate,
      endDate: _endDate,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<AnalyticsBloc, AnalyticsState>(
        listener: (context, state) {
          if (state is AnalyticsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Date Range Selector
            _buildDateRangeSelector(context),

            // Tab Bar
            Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                tabs: const [
                  Tab(text: 'Sales'),
                  Tab(text: 'Orders'),
                  Tab(text: 'Popular'),
                  Tab(text: 'Feedback'),
                ],
              ),
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  SalesReportTab(),
                  OrderTrendsTab(),
                  PopularDishesTab(),
                  CustomerFeedbackTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.read<AnalyticsBloc>().add(const AnalyticsRefreshRequested());
        },
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildDateRangeSelector(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => _selectDateRange(context),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.date_range,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${_formatDate(_startDate)} - ${_formatDate(_endDate)}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton.icon(
            onPressed: () {
              context
                  .read<AnalyticsBloc>()
                  .add(const AnalyticsRefreshRequested());
            },
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null && mounted) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });

      // Refresh data with new date range
      if (mounted) {
        context.read<AnalyticsBloc>().add(
              AnalyticsDateRangeChanged(
                startDate: _startDate,
                endDate: _endDate,
              ),
            );

        _loadInitialData();
      }
    }
  }
}
