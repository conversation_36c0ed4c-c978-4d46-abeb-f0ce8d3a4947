import 'package:flutter/material.dart';

class OrderStatsBar extends StatelessWidget {
  final int totalOrders;
  final int newOrders;
  final int preparingOrders;
  final int readyOrders;
  final double averagePreparationTime;

  const OrderStatsBar({
    super.key,
    required this.totalOrders,
    required this.newOrders,
    required this.preparingOrders,
    required this.readyOrders,
    this.averagePreparationTime = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Order Statistics',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                'Total: $totalOrders',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  context,
                  'New',
                  newOrders,
                  Colors.blue,
                  Icons.fiber_new,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Preparing',
                  preparingOrders,
                  Colors.orange,
                  Icons.schedule,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Ready',
                  readyOrders,
                  Colors.green,
                  Icons.check_circle,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  context,
                  'Avg Time',
                  averagePreparationTime.toInt(),
                  Colors.purple,
                  Icons.timer,
                  suffix: 'min',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    int value,
    Color color,
    IconData icon, {
    String suffix = '',
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '$value$suffix',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
