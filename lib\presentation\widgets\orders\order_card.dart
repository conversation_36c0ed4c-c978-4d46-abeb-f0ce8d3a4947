import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:supplier_app/core/constants/app_constants.dart';
import 'package:supplier_app/data/models/order.dart';

class OrderCard extends StatelessWidget {
  final Order order;
  final VoidCallback? onTap;
  final VoidCallback? onAccept;
  final VoidCallback? onReject;
  final Function(OrderStatus)? onStatusUpdate;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onAccept,
    this.onReject,
    this.onStatusUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with order ID and status
              _buildHeader(context),
              const SizedBox(height: 12),

              // Customer info
              _buildCustomerInfo(context),
              const SizedBox(height: 12),

              // Order items summary
              _buildOrderSummary(context),
              const SizedBox(height: 12),

              // Order details
              _buildOrderDetails(context),

              // Action buttons
              if (_shouldShowActions()) ...[
                const SizedBox(height: 16),
                _buildActionButtons(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Order #${order.id.substring(0, 8)}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            order.status.displayName,
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.person_outline,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            order.customerName,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
        Icon(
          Icons.access_time,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          DateFormat('HH:mm').format(order.orderTime),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      ],
    );
  }

  Widget _buildOrderSummary(BuildContext context) {
    final itemCount = order.items.length;
    final firstItem = order.items.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          itemCount == 1
              ? firstItem.dishName
              : '${firstItem.dishName} ${itemCount > 1 ? '+ ${itemCount - 1} more' : ''}',
          style: Theme.of(context).textTheme.bodyMedium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          '$itemCount item${itemCount > 1 ? 's' : ''}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      ],
    );
  }

  Widget _buildOrderDetails(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Total',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            Text(
              '\$${order.total.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
            ),
          ],
        ),
        if (order.estimatedPreparationTime > 0)
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Prep Time',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              Text(
                '${order.estimatedPreparationTime} min',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        if (onAccept != null)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onAccept,
              icon: const Icon(Icons.check, size: 16),
              label: const Text('Accept'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        if (onAccept != null && onReject != null) const SizedBox(width: 8),
        if (onReject != null)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onReject,
              icon: const Icon(Icons.close, size: 16),
              label: const Text('Reject'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),
        if (_canUpdateStatus())
          Expanded(
            child: ElevatedButton(
              onPressed: () => _showStatusUpdateDialog(context),
              child: Text(_getNextStatusText()),
            ),
          ),
      ],
    );
  }

  bool _shouldShowActions() {
    return onAccept != null || onReject != null || _canUpdateStatus();
  }

  bool _canUpdateStatus() {
    return onStatusUpdate != null &&
        (order.status.canPrepare ||
            order.status.canMarkReady ||
            order.status.canMarkPickedUp);
  }

  String _getNextStatusText() {
    if (order.status.canPrepare) return 'Start Preparing';
    if (order.status.canMarkReady) return 'Mark Ready';
    if (order.status.canMarkPickedUp) return 'Mark Picked Up';
    return 'Update Status';
  }

  void _showStatusUpdateDialog(BuildContext context) {
    OrderStatus? nextStatus;
    if (order.status.canPrepare) nextStatus = OrderStatus.preparing;
    if (order.status.canMarkReady) nextStatus = OrderStatus.ready;
    if (order.status.canMarkPickedUp) nextStatus = OrderStatus.pickedUp;

    if (nextStatus != null && onStatusUpdate != null) {
      onStatusUpdate!(nextStatus);
    }
  }

  Color _getStatusColor() {
    switch (order.status) {
      case OrderStatus.pending:
        return Colors.blue;
      case OrderStatus.accepted:
        return Colors.green;
      case OrderStatus.preparing:
        return Colors.orange;
      case OrderStatus.ready:
        return Colors.purple;
      case OrderStatus.pickedUp:
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
      case OrderStatus.rejected:
        return Colors.red;
    }
  }
}
