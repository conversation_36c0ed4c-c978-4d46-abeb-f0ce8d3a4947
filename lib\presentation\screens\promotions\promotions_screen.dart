import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/data/models/promotion.dart';
import 'package:supplier_app/presentation/bloc/promotions/promotions_bloc.dart';
import 'package:supplier_app/presentation/bloc/promotions/promotions_event.dart';
import 'package:supplier_app/presentation/bloc/promotions/promotions_state.dart';
import 'package:supplier_app/presentation/widgets/promotions/promotion_card.dart';
import 'package:supplier_app/presentation/widgets/common/empty_state_widget.dart';
import 'package:supplier_app/presentation/widgets/common/loading_widget.dart';

class PromotionsScreen extends StatefulWidget {
  const PromotionsScreen({super.key});

  @override
  State<PromotionsScreen> createState() => _PromotionsScreenState();
}

class _PromotionsScreenState extends State<PromotionsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);

    // Load initial promotions
    context.read<PromotionsBloc>().add(const PromotionsLoadRequested());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;

    bool? filter;
    switch (_tabController.index) {
      case 0: // Active
        filter = true;
        break;
      case 1: // Inactive
        filter = false;
        break;
      case 2: // All (including expired)
        filter = null;
        break;
    }

    context
        .read<PromotionsBloc>()
        .add(PromotionsFilterChanged(isActive: filter));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<PromotionsBloc, PromotionsState>(
        listener: (context, state) {
          if (state is PromotionsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is PromotionCreateSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Promotion "${state.promotion.title}" created successfully'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );
          } else if (state is PromotionUpdateSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Promotion "${state.promotion.title}" updated successfully'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );
          } else if (state is PromotionDeleteSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Promotion deleted successfully'),
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Tab Bar
              Container(
                color: Theme.of(context).scaffoldBackgroundColor,
                child: TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'Active'),
                    Tab(text: 'Inactive'),
                    Tab(text: 'All'),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: _buildContent(state),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _navigateToCreatePromotion(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent(PromotionsState state) {
    if (state is PromotionsLoading) {
      return const LoadingWidget(message: 'Loading promotions...');
    }

    if (state is PromotionsError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load promotions',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<PromotionsBloc>()
                    .add(const PromotionsLoadRequested());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state is PromotionsLoaded) {
      final promotions = _getFilteredPromotions(state);

      if (promotions.isEmpty) {
        return EmptyStateWidget(
          icon: Icons.local_offer_outlined,
          title: 'No Promotions',
          message: _getEmptyMessage(),
          actionText: 'Create Promotion',
          onActionPressed: () => _navigateToCreatePromotion(context),
        );
      }

      return RefreshIndicator(
        onRefresh: () async {
          context.read<PromotionsBloc>().add(
                const PromotionsLoadRequested(isRefresh: true),
              );
        },
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: promotions.length,
          itemBuilder: (context, index) {
            final promotion = promotions[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: PromotionCard(
                promotion: promotion,
                onEdit: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Edit promotion feature coming soon'),
                    ),
                  );
                },
                onDelete: () {
                  _showDeleteDialog(context, promotion.id, promotion.title);
                },
                onToggleStatus: (isActive) {
                  context.read<PromotionsBloc>().add(
                        PromotionStatusToggleRequested(promotion.id, isActive),
                      );
                },
                onViewUsage: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Promotion usage feature coming soon'),
                    ),
                  );
                },
              ),
            );
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }

  List<Promotion> _getFilteredPromotions(PromotionsLoaded state) {
    switch (_tabController.index) {
      case 0: // Active
        return state.activePromotions;
      case 1: // Inactive
        return state.inactivePromotions;
      case 2: // All
        return state.promotions;
      default:
        return state.promotions;
    }
  }

  String _getEmptyMessage() {
    switch (_tabController.index) {
      case 0:
        return 'No active promotions. Create one to boost your sales!';
      case 1:
        return 'No inactive promotions found.';
      case 2:
        return 'No promotions created yet. Start by creating your first promotion.';
      default:
        return 'No promotions found.';
    }
  }

  void _navigateToCreatePromotion(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Create promotion screen coming soon'),
      ),
    );
  }

  void _showDeleteDialog(
      BuildContext context, String promotionId, String promotionTitle) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Promotion'),
        content: Text(
          'Are you sure you want to delete "$promotionTitle"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              context.read<PromotionsBloc>().add(
                    PromotionDeleteRequested(promotionId),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
