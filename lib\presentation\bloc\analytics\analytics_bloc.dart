import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/data/repositories/analytics_repository.dart';
import 'analytics_event.dart';
import 'analytics_state.dart';

class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final AnalyticsRepository analyticsRepository;

  AnalyticsBloc({required this.analyticsRepository}) : super(const AnalyticsInitial()) {
    on<AnalyticsSalesReportRequested>(_onAnalyticsSalesReportRequested);
    on<AnalyticsOrderTrendsRequested>(_onAnalyticsOrderTrendsRequested);
    on<AnalyticsPopularDishesRequested>(_onAnalyticsPopularDishesRequested);
    on<AnalyticsCustomerFeedbackRequested>(_onAnalyticsCustomerFeedbackRequested);
    on<AnalyticsRevenueRequested>(_onAnalyticsRevenueRequested);
    on<AnalyticsPerformanceMetricsRequested>(_onAnalyticsPerformanceMetricsRequested);
    on<AnalyticsDateRangeChanged>(_onAnalyticsDateRangeChanged);
    on<AnalyticsRefreshRequested>(_onAnalyticsRefreshRequested);
  }

  Future<void> _onAnalyticsSalesReportRequested(
    AnalyticsSalesReportRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(AnalyticsSalesReportLoading(event.type));

    try {
      final salesReport = await analyticsRepository.getSalesReport(
        type: event.type,
        startDate: event.startDate,
        endDate: event.endDate,
      );

      emit(AnalyticsSalesReportLoaded(salesReport));
      
      // Update the main analytics state if it exists
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          salesReport: salesReport,
          reportType: event.type,
          startDate: event.startDate,
          endDate: event.endDate,
        ));
      }
    } catch (e) {
      emit(AnalyticsSalesReportError(e.toString()));
    }
  }

  Future<void> _onAnalyticsOrderTrendsRequested(
    AnalyticsOrderTrendsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsOrderTrendsLoading());

    try {
      final orderTrends = await analyticsRepository.getOrderTrends(
        startDate: event.startDate,
        endDate: event.endDate,
      );

      emit(AnalyticsOrderTrendsLoaded(orderTrends));
      
      // Update the main analytics state if it exists
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          orderTrends: orderTrends,
          startDate: event.startDate,
          endDate: event.endDate,
        ));
      }
    } catch (e) {
      emit(AnalyticsOrderTrendsError(e.toString()));
    }
  }

  Future<void> _onAnalyticsPopularDishesRequested(
    AnalyticsPopularDishesRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsPopularDishesLoading());

    try {
      final popularDishes = await analyticsRepository.getPopularDishes(
        startDate: event.startDate,
        endDate: event.endDate,
        limit: event.limit,
      );

      emit(AnalyticsPopularDishesLoaded(popularDishes));
      
      // Update the main analytics state if it exists
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          popularDishes: popularDishes,
          startDate: event.startDate,
          endDate: event.endDate,
        ));
      }
    } catch (e) {
      emit(AnalyticsPopularDishesError(e.toString()));
    }
  }

  Future<void> _onAnalyticsCustomerFeedbackRequested(
    AnalyticsCustomerFeedbackRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsCustomerFeedbackLoading());

    try {
      final customerFeedback = await analyticsRepository.getCustomerFeedback(
        startDate: event.startDate,
        endDate: event.endDate,
        minRating: event.minRating,
        maxRating: event.maxRating,
      );

      emit(AnalyticsCustomerFeedbackLoaded(customerFeedback));
      
      // Update the main analytics state if it exists
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          customerFeedback: customerFeedback,
          startDate: event.startDate,
          endDate: event.endDate,
        ));
      }
    } catch (e) {
      emit(AnalyticsCustomerFeedbackError(e.toString()));
    }
  }

  Future<void> _onAnalyticsRevenueRequested(
    AnalyticsRevenueRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsRevenueLoading());

    try {
      final revenueAnalytics = await analyticsRepository.getRevenueAnalytics(
        startDate: event.startDate,
        endDate: event.endDate,
      );

      emit(AnalyticsRevenueLoaded(revenueAnalytics));
      
      // Update the main analytics state if it exists
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          revenueAnalytics: revenueAnalytics,
          startDate: event.startDate,
          endDate: event.endDate,
        ));
      }
    } catch (e) {
      emit(AnalyticsRevenueError(e.toString()));
    }
  }

  Future<void> _onAnalyticsPerformanceMetricsRequested(
    AnalyticsPerformanceMetricsRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(const AnalyticsPerformanceMetricsLoading());

    try {
      final performanceMetrics = await analyticsRepository.getPerformanceMetrics(
        startDate: event.startDate,
        endDate: event.endDate,
      );

      emit(AnalyticsPerformanceMetricsLoaded(performanceMetrics));
      
      // Update the main analytics state if it exists
      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(currentState.copyWith(
          performanceMetrics: performanceMetrics,
          startDate: event.startDate,
          endDate: event.endDate,
        ));
      }
    } catch (e) {
      emit(AnalyticsPerformanceMetricsError(e.toString()));
    }
  }

  Future<void> _onAnalyticsDateRangeChanged(
    AnalyticsDateRangeChanged event,
    Emitter<AnalyticsState> emit,
  ) async {
    if (state is AnalyticsLoaded) {
      final currentState = state as AnalyticsLoaded;
      emit(currentState.copyWith(
        startDate: event.startDate,
        endDate: event.endDate,
      ));
    }
  }

  Future<void> _onAnalyticsRefreshRequested(
    AnalyticsRefreshRequested event,
    Emitter<AnalyticsState> emit,
  ) async {
    if (state is AnalyticsLoaded) {
      final currentState = state as AnalyticsLoaded;
      emit(currentState.copyWith(isRefreshing: true));
      
      try {
        // Refresh all loaded data
        final futures = <Future>[];
        
        if (currentState.salesReport != null && currentState.reportType != null) {
          futures.add(analyticsRepository.getSalesReport(
            type: currentState.reportType!,
            startDate: currentState.startDate,
            endDate: currentState.endDate,
          ));
        }
        
        if (currentState.orderTrends != null) {
          futures.add(analyticsRepository.getOrderTrends(
            startDate: currentState.startDate ?? DateTime.now().subtract(const Duration(days: 30)),
            endDate: currentState.endDate ?? DateTime.now(),
          ));
        }
        
        if (currentState.popularDishes != null) {
          futures.add(analyticsRepository.getPopularDishes(
            startDate: currentState.startDate,
            endDate: currentState.endDate,
          ));
        }
        
        if (currentState.customerFeedback != null) {
          futures.add(analyticsRepository.getCustomerFeedback(
            startDate: currentState.startDate,
            endDate: currentState.endDate,
          ));
        }
        
        if (currentState.revenueAnalytics != null) {
          futures.add(analyticsRepository.getRevenueAnalytics(
            startDate: currentState.startDate ?? DateTime.now().subtract(const Duration(days: 30)),
            endDate: currentState.endDate ?? DateTime.now(),
          ));
        }
        
        if (currentState.performanceMetrics != null) {
          futures.add(analyticsRepository.getPerformanceMetrics(
            startDate: currentState.startDate,
            endDate: currentState.endDate,
          ));
        }
        
        await Future.wait(futures);
        
        emit(currentState.copyWith(isRefreshing: false));
      } catch (e) {
        emit(currentState.copyWith(isRefreshing: false));
        emit(AnalyticsError(e.toString()));
      }
    }
  }
}
