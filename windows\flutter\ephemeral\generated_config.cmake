# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\Software File for flutter\\flutter_windows_3.24.3-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Flutter Git Hub\\Food Delivery App\\Foodie_supplier_app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\Software File for flutter\\flutter_windows_3.24.3-stable\\flutter"
  "PROJECT_DIR=D:\\Flutter Git Hub\\Food Delivery App\\Foodie_supplier_app"
  "FLUTTER_ROOT=D:\\Software File for flutter\\flutter_windows_3.24.3-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Flutter Git Hub\\Food Delivery App\\Foodie_supplier_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Flutter Git Hub\\Food Delivery App\\Foodie_supplier_app"
  "FLUTTER_TARGET=D:\\Flutter Git Hub\\Food Delivery App\\Foodie_supplier_app\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Flutter Git Hub\\Food Delivery App\\Foodie_supplier_app\\.dart_tool\\package_config.json"
)
