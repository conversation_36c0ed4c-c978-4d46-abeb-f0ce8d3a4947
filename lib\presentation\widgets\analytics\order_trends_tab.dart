import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_bloc.dart';
import 'package:supplier_app/presentation/bloc/analytics/analytics_state.dart';
import 'package:supplier_app/presentation/widgets/common/loading_widget.dart';
import 'package:supplier_app/presentation/widgets/common/empty_state_widget.dart';

class OrderTrendsTab extends StatelessWidget {
  const OrderTrendsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnalyticsBloc, AnalyticsState>(
      builder: (context, state) {
        if (state is AnalyticsLoading || state is AnalyticsOrderTrendsLoading) {
          return const LoadingWidget(message: 'Loading order trends...');
        }

        if (state is AnalyticsError || state is AnalyticsOrderTrendsError) {
          final message = state is AnalyticsError 
              ? state.message 
              : (state as AnalyticsOrderTrendsError).message;
          
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load order trends',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Retry loading order trends
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return const EmptyStateWidget(
          icon: Icons.trending_up_outlined,
          title: 'Order Trends',
          message: 'Order trend analysis coming soon with interactive charts.',
        );
      },
    );
  }
}
