# Supplier (Restaurant) App - Food Delivery Platform

A comprehensive Flutter application for restaurant owners and managers to manage their food delivery operations. Built with **Bloc state management** and following clean architecture principles.

## 🚀 Features

### 📱 Core Screens

#### 🔐 Authentication
- **Login Screen**: Restaurant ID/Email and Password authentication
- Secure token-based authentication with refresh capability
- Remember me functionality

#### 📊 Dashboard
- **Summary Cards**: New Orders, Orders in Progress, Completed Orders, Daily Sales
- **Order Alerts/Notifications**: Real-time order updates
- **Restaurant Status Toggle**: Online/Offline/Busy status management
- **Recent Orders List**: Quick overview of latest orders

#### 📋 Order Management
- **Comprehensive Filtering**: New Orders, Pending, Preparing, Ready for Pickup, Completed, Cancelled
- **Order Actions**: Accept, Reject, Mark as Preparing, Mark as Ready
- **Order Details View**: Complete order information with customer details
- **Real-time Updates**: Live order status tracking

#### 🍽️ Menu Management
- **Categories Management**: Add/Edit/Delete menu categories
- **Dish Management**: Complete CRUD operations for dishes
- **Availability Toggle**: Quick enable/disable dishes
- **Image Upload**: Support for dish and category images
- **Customization Options**: Size, toppings, and other variations

#### 🎯 Promotions Management
- **Active/Inactive Promotions**: Comprehensive promotion lifecycle
- **Multiple Promotion Types**: Percentage, Fixed Amount, BOGO, Free Delivery
- **Usage Tracking**: Monitor promotion performance
- **Scheduling**: Start/end date management

#### 📈 Analytics & Reports

- **Sales Reports**: Daily, weekly, monthly revenue analysis
- **Order Trends**: Volume and performance metrics
- **Popular Dishes**: Best-selling items analysis
- **Customer Feedback**: Ratings and reviews summary

## 🏗️ Architecture

### 📁 Project Structure

```
lib/
├── core/
│   ├── constants/
│   │   └── app_constants.dart          # App-wide constants and enums
│   ├── theme/
│   │   └── app_theme.dart              # Material Design theme
│   └── utils/
│       ├── date_utils.dart             # Date formatting utilities
│       └── validators.dart             # Form validation helpers
├── data/
│   ├── models/                         # Data models
│   │   ├── analytics.dart
│   │   ├── menu.dart
│   │   ├── order.dart
│   │   ├── promotion.dart
│   │   ├── restaurant.dart
│   │   └── user.dart
│   └── repositories/                   # Data layer
│       ├── analytics_repository.dart
│       ├── auth_repository.dart
│       ├── menu_repository.dart
│       ├── order_repository.dart
│       └── promotion_repository.dart
├── presentation/
│   ├── bloc/                          # Business Logic Components
│   │   ├── analytics/
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── menu/
│   │   ├── orders/
│   │   └── promotions/
│   ├── screens/                       # UI Screens
│   │   ├── analytics/
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── main/
│   │   ├── menu/
│   │   ├── orders/
│   │   └── promotions/
│   └── widgets/                       # Reusable UI Components
│       ├── common/
│       ├── dashboard/
│       └── orders/
└── main.dart                         # App entry point
```

### 🔧 State Management

The app uses **Flutter Bloc** for state management with the following pattern:

- **Events**: User actions and external triggers
- **States**: UI state representations
- **Blocs**: Business logic processors

### 🎨 Design System

- **Material Design 3**: Modern, accessible UI components
- **Custom Theme**: Restaurant/food industry optimized colors
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Consistent Typography**: Hierarchical text styles

## 🛠️ Technical Stack

### 📦 Dependencies

```yaml
dependencies:
  flutter: sdk
  flutter_bloc: ^8.1.3          # State management
  equatable: ^2.0.5             # Value equality
  http: ^1.1.0                  # HTTP client
  shared_preferences: ^2.2.2     # Local storage
  image_picker: ^1.0.4          # Image selection
  cached_network_image: ^3.3.0   # Image caching
  fl_chart: ^0.64.0             # Charts and graphs
  intl: ^0.18.1                 # Internationalization
```

### 🔑 Key Features

- **Clean Architecture**: Separation of concerns with clear layers
- **Type Safety**: Comprehensive model classes with JSON serialization
- **Error Handling**: Robust error management with user-friendly messages
- **Offline Support**: Local data caching and sync capabilities
- **Performance**: Optimized with lazy loading and efficient state management

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code
- Android/iOS device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd supplier_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### Configuration

1. **API Configuration**: Update `AppConstants.baseUrl` in `lib/core/constants/app_constants.dart`
2. **Authentication**: Configure your authentication endpoints
3. **Image Upload**: Set up image storage service URLs

## 📱 Usage

### Login

- Use restaurant credentials to access the dashboard
- Toggle "Remember me" for persistent login

### Dashboard

- Monitor real-time order statistics
- Toggle restaurant status (Online/Offline/Busy)
- Quick access to recent orders

### Order Management

- Filter orders by status using tabs
- Accept/reject new orders with reasons
- Update order status through the workflow
- View detailed order information

### Menu Management

- Organize dishes into categories
- Upload images for visual appeal
- Set availability and pricing
- Configure customization options

### Promotions

- Create time-bound promotional offers
- Track usage and performance
- Manage active/inactive promotions

### Analytics

- View comprehensive sales reports
- Analyze order trends and patterns
- Identify popular dishes
- Monitor customer feedback

## 🔮 Future Enhancements

- [ ] Push notifications for new orders
- [ ] Real-time chat with customers
- [ ] Inventory management integration
- [ ] Multi-location support
- [ ] Advanced analytics with ML insights
- [ ] Voice order management
- [ ] Integration with POS systems

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Built with ❤️ using Flutter and Bloc**
